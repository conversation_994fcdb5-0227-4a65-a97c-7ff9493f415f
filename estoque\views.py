from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Count, F, Q, Avg, Case, When, Value, IntegerField, Prefetch, OuterRef, Subquery
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django.core.cache import cache
from datetime import timedelta, date, datetime
import json
import logging
import os
import psutil
import time

from .models import (
    MaterialPadrao, Material, Mola, MovimentacaoEstoque, MovimentacaoMaterial,
    Pedido<PERSON>enda, ItemPedido, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>e<PERSON><PERSON><PERSON>cencia,
    ItemPlanejamento
)

# Importar módulo de previsão avançada
try:
    from .forecasting import (
        generate_forecast, generate_all_forecasts,
        STATSMODELS_INSTALLED, PROPHET_INSTALLED, SKLEARN_INSTALLED
    )
    ADVANCED_FORECASTING_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("Módulo de previsão avançada carregado com sucesso.")
except ImportError as e:
    ADVANCED_FORECASTING_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"Não foi possível carregar o módulo de previsão avançada: {str(e)}")
    logger.warning("Usando métodos de previsão básicos.")
from .forms import (MaterialPadraoForm, MaterialForm, MolaForm, MovimentacaoEstoqueForm, MovimentacaoMaterialForm,
                   PedidoVendaForm, ItemPedidoForm, RelatorioMolasForm, RelatorioEstoqueForm,
                   MovimentacaoMultiplaForm)
from .utils import ordenar_materiais, extrair_valor_numerico_diametro, extrair_valor_numerico_nome_mola
from .filters import (MolaFilter, MaterialFilter, MovimentacaoEstoqueFilter,
                     MovimentacaoMaterialFilter, PedidoVendaFilter)

import io
import csv
import re
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer, Image as RLImage
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
import matplotlib.pyplot as plt
import numpy as np
from .report_utils import get_styles, add_header_footer, create_table_style, create_chart, create_line_chart


# Função para monitorar uso de recursos
def get_system_resources():
    """Obtém informações sobre uso de recursos do sistema"""
    process = psutil.Process(os.getpid())

    # Uso de memória
    memory_info = process.memory_info()
    memory_usage_mb = memory_info.rss / 1024 / 1024  # Converter para MB

    # Uso de CPU
    cpu_percent = process.cpu_percent(interval=0.1)

    # Número de threads
    num_threads = process.num_threads()

    # Tempo de execução
    process_time = process.create_time()
    uptime_seconds = time.time() - process_time

    return {
        'memory_usage_mb': round(memory_usage_mb, 2),
        'cpu_percent': round(cpu_percent, 2),
        'num_threads': num_threads,
        'uptime_hours': round(uptime_seconds / 3600, 2)
    }

# Views para Dashboard
@cache_page(60 * 5)  # Cache por 5 minutos
def dashboard(request):
    """View para o dashboard principal"""
    # Usar cache para consultas frequentes
    cache_key = 'dashboard_data'
    dashboard_data = cache.get(cache_key)

    if not dashboard_data:
        logger.info("Gerando dados do dashboard (cache miss)")

        # Otimizar consultas usando agregações
        # Fazer contagens e alertas em uma única consulta para cada modelo
        molas_stats = Mola.objects.aggregate(
            total=Count('id'),
            estoque_baixo=Count('id', filter=Q(quantidade_estoque__lte=F('estoque_minimo')))
        )

        materiais_stats = Material.objects.aggregate(
            total=Count('id'),
            estoque_baixo=Count('id', filter=Q(quantidade_estoque__lte=F('estoque_minimo')))
        )

        # Movimentações recentes - usar select_related para otimizar consultas
        # Limitar campos retornados para melhorar desempenho
        movimentacoes_recentes = list(
            MovimentacaoEstoque.objects.select_related('mola')
            .only('id', 'tipo', 'quantidade', 'data', 'mola__codigo', 'mola__cliente')
            .order_by('-data')[:10]
        )

        # Molas mais vendidas (últimos 30 dias)
        molas_mais_vendidas = list(Mola.mais_vendidas(periodo='mes')[:5])

        dashboard_data = {
            'total_molas': molas_stats['total'],
            'total_materiais': materiais_stats['total'],
            'molas_estoque_baixo': molas_stats['estoque_baixo'],
            'materiais_estoque_baixo': materiais_stats['estoque_baixo'],
            'movimentacoes_recentes': movimentacoes_recentes,
            'molas_mais_vendidas': molas_mais_vendidas,
            'timestamp': timezone.now()
        }

        # Armazenar no cache por 5 minutos
        cache.set(cache_key, dashboard_data, 60 * 5)
    else:
        logger.info("Usando dados do dashboard em cache (cache hit)")

    # Adicionar informações de recursos do sistema
    try:
        system_resources = get_system_resources()
        dashboard_data.update(system_resources)
    except Exception as e:
        logger.error(f"Erro ao obter recursos do sistema: {str(e)}")

    return render(request, 'estoque/dashboard.html', dashboard_data)


def dashboard_kpi(request):
    """Dashboard avançado com KPIs e gráficos"""
    # Obter parâmetros de filtro
    periodo = request.GET.get('periodo', '30')
    cliente = request.GET.get('cliente', '')
    categoria = request.GET.get('categoria', '')

    # Converter período para inteiro
    try:
        dias = int(periodo)
    except ValueError:
        dias = 30

    # Calcular datas
    hoje = timezone.now().date()
    data_inicio = hoje - timezone.timedelta(days=dias)
    data_inicio_anterior = data_inicio - timezone.timedelta(days=dias)  # Período anterior para comparação

    # Filtrar movimentações por período
    movimentacoes = MovimentacaoEstoque.objects.filter(data__gte=data_inicio)
    movimentacoes_anterior = MovimentacaoEstoque.objects.filter(
        data__gte=data_inicio_anterior,
        data__lt=data_inicio
    )

    # Filtrar por cliente se especificado
    if cliente:
        movimentacoes = movimentacoes.filter(mola__cliente__icontains=cliente)
        movimentacoes_anterior = movimentacoes_anterior.filter(mola__cliente__icontains=cliente)

    # Filtrar por categoria se especificada
    if categoria:
        # Obter molas da categoria especificada
        analises = AnaliseObsolescencia.objects.filter(classificacao=categoria)
        molas_ids = analises.values_list('mola_id', flat=True)

        movimentacoes = movimentacoes.filter(mola_id__in=molas_ids)
        movimentacoes_anterior = movimentacoes_anterior.filter(mola_id__in=molas_ids)

    # Calcular KPIs

    # 1. Rotatividade de Estoque
    # Fórmula: Vendas no período / Estoque médio
    vendas_periodo = movimentacoes.filter(tipo='S').aggregate(total=Sum('quantidade'))['total'] or 0
    vendas_anterior = movimentacoes_anterior.filter(tipo='S').aggregate(total=Sum('quantidade'))['total'] or 0

    estoque_atual = Mola.objects.aggregate(total=Sum('quantidade_estoque'))['total'] or 0

    # Estimar estoque no início do período (estoque atual + saídas - entradas)
    entradas_periodo = movimentacoes.filter(tipo='E').aggregate(total=Sum('quantidade'))['total'] or 0
    saidas_periodo = vendas_periodo
    estoque_inicio = estoque_atual + saidas_periodo - entradas_periodo

    # Estoque médio no período
    estoque_medio = (estoque_atual + estoque_inicio) / 2 if (estoque_atual + estoque_inicio) != 0 else estoque_atual

    # Calcular rotatividade
    rotatividade_estoque = vendas_periodo / estoque_medio if estoque_medio > 0 else 0

    # Calcular tendência (comparação com período anterior)
    rotatividade_anterior = vendas_anterior / estoque_medio if estoque_medio > 0 else 0
    rotatividade_tendencia = ((rotatividade_estoque - rotatividade_anterior) / rotatividade_anterior * 100) if rotatividade_anterior > 0 else 0
    rotatividade_tendencia_abs = abs(rotatividade_tendencia) if rotatividade_tendencia < 0 else rotatividade_tendencia

    # 2. Cobertura de Estoque
    # Fórmula: Estoque atual / (Vendas no período / Número de dias)
    vendas_diarias = vendas_periodo / dias if dias > 0 else 0
    cobertura_estoque = estoque_atual / vendas_diarias if vendas_diarias > 0 else 0

    # 3. Taxa de Atendimento
    # Percentual de pedidos atendidos completamente
    pedidos_periodo = PedidoVenda.objects.filter(data_pedido__gte=data_inicio)

    if cliente:
        pedidos_periodo = pedidos_periodo.filter(cliente__icontains=cliente)

    total_pedidos = pedidos_periodo.count()
    pedidos_atendidos = pedidos_periodo.annotate(
        itens_total=Count('itens'),
        itens_atendidos=Count('itens', filter=Q(itens__atendido=True))
    ).filter(itens_total=F('itens_atendidos')).count()

    taxa_atendimento = (pedidos_atendidos / total_pedidos * 100) if total_pedidos > 0 else 0

    # 4. Valor em Estoque
    # Valor total do estoque atual (usando custo médio estimado)
    custo_medio = 0.001  # Valor estimado por unidade
    valor_estoque = estoque_atual * custo_medio

    # Valor do estoque no período anterior
    valor_estoque_anterior = estoque_inicio * custo_medio
    valor_estoque_tendencia = ((valor_estoque - valor_estoque_anterior) / valor_estoque_anterior * 100) if valor_estoque_anterior > 0 else 0
    valor_estoque_tendencia_abs = abs(valor_estoque_tendencia) if valor_estoque_tendencia < 0 else valor_estoque_tendencia

    # Dados para gráficos

    # 1. Tendência de Vendas
    # Agrupar vendas por dia/semana/mês
    vendas_diarias_dict = {}

    # Inicializar com zeros para todos os dias no período
    for i in range(dias):
        data = hoje - timezone.timedelta(days=i)
        vendas_diarias_dict[data.strftime('%Y-%m-%d')] = 0

    # Preencher com dados reais
    vendas_por_dia = movimentacoes.filter(tipo='S').values('data').annotate(
        total=Sum('quantidade')
    ).order_by('data')

    for venda in vendas_por_dia:
        data_str = venda['data'].strftime('%Y-%m-%d')
        vendas_diarias_dict[data_str] = venda['total']

    # Ordenar por data
    vendas_labels = []
    vendas_dados = []

    for data_str, quantidade in sorted(vendas_diarias_dict.items()):
        vendas_labels.append(data_str)
        vendas_dados.append(quantidade)

    # Gerar previsão simples para os próximos 7 dias
    vendas_previsao = [None] * (len(vendas_dados) - 7)  # Nulos para dados históricos

    # Calcular média móvel dos últimos 7 dias para previsão
    if len(vendas_dados) >= 7:
        media_movel = sum(vendas_dados[-7:]) / 7
        for _ in range(7):
            vendas_previsao.append(media_movel)

    # 2. Distribuição por Categoria
    categorias_dados = [0, 0, 0, 0, 0]  # A, B, C, D, O

    # Contar molas por categoria em uma única consulta
    categorias_count = AnaliseObsolescencia.objects.values('classificacao').annotate(
        count=Count('id')
    ).order_by('classificacao')

    # Mapear os resultados para o array
    categorias_map = {'A': 0, 'B': 1, 'C': 2, 'D': 3, 'O': 4}
    for item in categorias_count:
        if item['classificacao'] in categorias_map:
            categorias_dados[categorias_map[item['classificacao']]] = item['count']

    # 3. Top 5 Molas
    top_molas = Mola.mais_vendidas(periodo=periodo)[:5]

    # Calcular percentual do total
    total_vendido = sum(mola['total_vendido'] for mola in top_molas)

    for mola in top_molas:
        mola['percentual'] = (mola['total_vendido'] / total_vendido * 100) if total_vendido > 0 else 0

    # 4. Alertas e Recomendações
    alertas = []

    # Consultar todos os alertas em uma única consulta
    # Alerta de estoque baixo e itens obsoletos
    molas_estoque_baixo = Mola.objects.filter(quantidade_estoque__lte=F('estoque_minimo')).count()
    itens_obsoletos = categorias_count.filter(classificacao='O').first()
    itens_obsoletos_count = itens_obsoletos['count'] if itens_obsoletos else 0

    if molas_estoque_baixo > 0:
        alertas.append({
            'titulo': f'{molas_estoque_baixo} molas com estoque abaixo do mínimo',
            'descricao': 'Verifique as molas com estoque abaixo do mínimo e crie ordens de fabricação.',
            'nivel': 'alto' if molas_estoque_baixo > 5 else 'medio',
            'data': hoje,
            'acao': 'Ver Molas',
            'url': '/estoque/alertas/estoque/'
        })

    if itens_obsoletos_count > 0:
        alertas.append({
            'titulo': f'{itens_obsoletos_count} itens classificados como obsoletos',
            'descricao': 'Considere estratégias para reduzir o estoque de itens obsoletos.',
            'nivel': 'medio',
            'data': hoje,
            'acao': 'Ver Análise',
            'url': '/analise-obsolescencia/'
        })

    # Alerta de cobertura de estoque baixa
    if cobertura_estoque < 15:  # Menos de 15 dias de cobertura
        alertas.append({
            'titulo': 'Cobertura de estoque baixa',
            'descricao': f'A cobertura média de estoque é de apenas {cobertura_estoque:.0f} dias.',
            'nivel': 'alto' if cobertura_estoque < 7 else 'medio',
            'data': hoje,
            'acao': 'Criar Ordem de Fabricação',
            'url': '/planejamento/'
        })

    # Alerta de taxa de atendimento baixa
    if taxa_atendimento < 90:  # Menos de 90% de atendimento
        alertas.append({
            'titulo': 'Taxa de atendimento abaixo do ideal',
            'descricao': f'Apenas {taxa_atendimento:.1f}% dos pedidos foram atendidos completamente.',
            'nivel': 'alto' if taxa_atendimento < 75 else 'medio',
            'data': hoje,
            'acao': 'Ver Pedidos',
            'url': '/pedidos/'
        })

    context = {
        'periodo': periodo,
        'cliente': cliente,
        'categoria': categoria,
        'rotatividade_estoque': rotatividade_estoque,
        'rotatividade_tendencia': rotatividade_tendencia,
        'rotatividade_tendencia_abs': rotatividade_tendencia_abs,
        'cobertura_estoque': cobertura_estoque,
        'taxa_atendimento': taxa_atendimento,
        'valor_estoque': valor_estoque,
        'valor_estoque_tendencia': valor_estoque_tendencia,
        'valor_estoque_tendencia_abs': valor_estoque_tendencia_abs,
        'vendas_labels': json.dumps(vendas_labels),
        'vendas_dados': json.dumps(vendas_dados),
        'vendas_previsao': json.dumps(vendas_previsao),
        'categorias_dados': json.dumps(categorias_dados),
        'top_molas': top_molas,
        'alertas': alertas,
    }

    return render(request, 'estoque/dashboard_kpi.html', context)


# Views para Molas
class MolaListView(ListView):
    model = Mola
    template_name = 'estoque/mola_list.html'
    context_object_name = 'molas'
    paginate_by = 20  # Aumentar para reduzir número de requisições de paginação

    def get_queryset(self):
        # Otimizar consultas usando select_related e only para limitar campos
        queryset = (
            super().get_queryset()
            .select_related('material', 'material_padrao')
            .only(
                'id', 'codigo', 'nome_mola', 'cliente', 'quantidade_estoque',
                'estoque_minimo', 'material__nome', 'material__diametro',
                'material_padrao__nome', 'ativo'
            )
        )

        # Aplicar filtros
        self.filterset = MolaFilter(self.request.GET, queryset=queryset)
        filtered_qs = self.filterset.qs

        # Aplicar ordenação numérica por nome_mola
        molas_list = list(filtered_qs)
        molas_ordenadas = sorted(molas_list, key=lambda m: extrair_valor_numerico_nome_mola(m.nome_mola or m.codigo))

        return molas_ordenadas

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset

        # Adicionar timestamp para mostrar quando os dados foram atualizados
        context['timestamp'] = timezone.now()

        # Adicionar informações de recursos do sistema
        try:
            context['system_resources'] = get_system_resources()
        except Exception as e:
            logger.error(f"Erro ao obter recursos do sistema: {str(e)}")

        # Adicionar contadores para o cabeçalho
        context['total_molas'] = self.filterset.qs.count()
        context['estoque_baixo_count'] = self.filterset.qs.filter(
            quantidade_estoque__lte=F('estoque_minimo')
        ).count()

        return context

    def dispatch(self, request, *args, **kwargs):
        # Desativar cache para esta view para garantir dados atualizados
        response = super().dispatch(request, *args, **kwargs)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response


class MolaDetailView(DetailView):
    model = Mola
    template_name = 'estoque/mola_detail.html'
    context_object_name = 'mola'

    def get_queryset(self):
        # Otimizar consulta usando select_related
        return super().get_queryset().select_related('material')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mola = self.get_object()

        # Histórico de movimentações - otimizar consulta
        # Removido select_related('mola') pois já estamos filtrando por mola.movimentacoes.all()
        context['movimentacoes'] = (
            mola.movimentacoes.all()
            .only('id', 'tipo', 'quantidade', 'data', 'ordem_venda', 'observacao')
            .order_by('-data')[:10]
        )

        # Verificar se há material suficiente para produção
        if mola.verificar_estoque_minimo() and mola.material:
            # Se estiver abaixo do mínimo e tiver material associado, calcular necessidade de material
            quantidade_necessaria = mola.estoque_minimo - mola.quantidade_estoque
            context['necessidade_material'] = mola.verificar_material_suficiente(quantidade_necessaria)

            # Adicionar informação sobre prazo de reposição
            context['dias_para_reposicao'] = 7  # Valor estimado

        # Calcular consumo total de material para o estoque atual
        if mola.peso_unitario and mola.material:
            # Converter de gramas para quilogramas (dividir por 1000)
            context['consumo_total'] = float(mola.peso_unitario) / 1000 * mola.quantidade_estoque

        # Adicionar estatísticas de vendas
        hoje = timezone.now().date()
        data_inicio = hoje - timezone.timedelta(days=30)

        vendas_recentes = (
            mola.movimentacoes
            .filter(tipo='S', data__gte=data_inicio)
            .aggregate(
                total=Sum('quantidade'),
                media_diaria=Avg('quantidade')
            )
        )

        context['vendas_recentes'] = vendas_recentes

        # Calcular tempo estimado para esgotar o estoque
        if vendas_recentes['media_diaria'] and vendas_recentes['media_diaria'] > 0:
            context['dias_ate_esgotar'] = int(mola.quantidade_estoque / vendas_recentes['media_diaria'])

        return context


class MolaCreateView(CreateView):
    model = Mola
    form_class = MolaForm
    template_name = 'estoque/mola_form.html'
    success_url = reverse_lazy('mola-list')

    def form_valid(self, form):
        # Verificar se o valor do peso_unitario foi enviado no formulário
        peso_unitario_raw = self.request.POST.get('peso_unitario')

        if peso_unitario_raw:
            # Converter o valor do formulário (substituindo vírgula por ponto)
            from decimal import Decimal
            try:
                peso_unitario_raw = peso_unitario_raw.replace(',', '.')
                form.instance.peso_unitario = Decimal(peso_unitario_raw).quantize(Decimal('0.0001'))
            except:
                # Se houver erro na conversão, usar o valor processado pelo formulário
                if 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
                    form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        elif 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
            # Usar o valor processado pelo formulário
            form.instance.peso_unitario = form.cleaned_data['peso_unitario']

        messages.success(self.request, 'Mola cadastrada com sucesso!')
        return super().form_valid(form)


class MolaUpdateView(UpdateView):
    model = Mola
    form_class = MolaForm
    template_name = 'estoque/mola_form.html'
    success_url = reverse_lazy('mola-list')

    def get_object(self, queryset=None):
        """Sobrescreve o método para garantir que o objeto seja carregado corretamente"""
        obj = super().get_object(queryset)
        # Garantir que o objeto seja carregado do banco de dados
        if obj.pk:
            # Recarregar o objeto do banco de dados para garantir que todos os campos estejam atualizados
            obj = Mola.objects.get(pk=obj.pk)
        return obj

    def get_initial(self):
        """Define os valores iniciais para o formulário"""
        initial = super().get_initial()
        # Obter o objeto Mola diretamente do banco de dados
        if self.object:
            # Forçar a inclusão do valor do peso_unitario nos valores iniciais
            if self.object.peso_unitario is not None:
                initial['peso_unitario'] = self.object.peso_unitario
        return initial

    def get_form_kwargs(self):
        """Retorna os kwargs para inicializar o formulário"""
        kwargs = super().get_form_kwargs()
        # Se o objeto existir e tiver um valor para peso_unitario, forçar a inclusão no data
        if self.object and self.object.peso_unitario is not None:
            # Criar uma cópia do data para não modificar o original
            if kwargs.get('data'):
                data = kwargs['data'].copy()
                # Adicionar o valor do peso_unitario ao data
                data[self.get_prefix() + '-peso_unitario' if self.get_prefix() else 'peso_unitario'] = self.object.peso_unitario
                kwargs['data'] = data
        return kwargs

    def get_form(self, form_class=None):
        """Inicializa o formulário com os dados da mola existente"""
        form = super().get_form(form_class)
        # Forçar a definição do valor do campo peso_unitario diretamente no widget
        if self.object and self.object.peso_unitario is not None:
            # Definir o valor diretamente no widget
            form.fields['peso_unitario'].widget.attrs['value'] = self.object.peso_unitario
            # Forçar o valor no campo
            form.initial['peso_unitario'] = self.object.peso_unitario
            # Forçar o valor no data do formulário
            if hasattr(form, 'data') and form.data:
                form.data = form.data.copy()
                form.data[form.add_prefix('peso_unitario')] = str(self.object.peso_unitario)
        return form

    def get_context_data(self, **kwargs):
        """Adiciona dados adicionais ao contexto do template"""
        context = super().get_context_data(**kwargs)
        # Adicionar o valor do peso_unitario diretamente ao contexto
        if self.object and self.object.peso_unitario is not None:
            context['peso_unitario_valor'] = self.object.peso_unitario
        return context

    def form_valid(self, form):
        # Verificar se o valor do peso_unitario foi enviado no formulário
        peso_unitario_raw = self.request.POST.get('peso_unitario')

        if peso_unitario_raw:
            # Converter o valor do formulário (substituindo vírgula por ponto)
            from decimal import Decimal
            try:
                peso_unitario_raw = peso_unitario_raw.replace(',', '.')
                form.instance.peso_unitario = Decimal(peso_unitario_raw).quantize(Decimal('0.0001'))
            except:
                # Se houver erro na conversão, usar o valor processado pelo formulário
                if 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
                    form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        elif 'peso_unitario' in form.cleaned_data and form.cleaned_data['peso_unitario'] is not None:
            # Usar o valor processado pelo formulário
            form.instance.peso_unitario = form.cleaned_data['peso_unitario']
        # Se o campo estiver vazio mas havia um valor anterior, manter o valor anterior
        elif self.object and self.object.peso_unitario is not None:
            form.instance.peso_unitario = self.object.peso_unitario

        messages.success(self.request, 'Mola atualizada com sucesso!')
        return super().form_valid(form)


class MolaDeleteView(DeleteView):
    model = Mola
    template_name = 'estoque/mola_confirm_delete.html'
    success_url = reverse_lazy('mola-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mola = self.get_object()

        # Verificar se a mola está sendo usada em pedidos
        itens_pedido = ItemPedido.objects.filter(mola=mola)
        context['itens_pedido'] = itens_pedido
        context['tem_pedidos'] = itens_pedido.exists()

        # Verificar se a mola está sendo usada em ordens de fabricação
        itens_planejamento = ItemPlanejamento.objects.filter(mola=mola)
        context['itens_planejamento'] = itens_planejamento
        context['tem_planejamentos'] = itens_planejamento.exists()

        return context

    def post(self, request, *args, **kwargs):
        mola = self.get_object()

        # Verificar se a mola está sendo usada em pedidos ou ordens de fabricação
        itens_pedido = ItemPedido.objects.filter(mola=mola)
        itens_planejamento = ItemPlanejamento.objects.filter(mola=mola)

        # Se não estiver sendo usada, excluir normalmente
        if not itens_pedido.exists() and not itens_planejamento.exists():
            return self.delete(request, *args, **kwargs)

        # Se estiver sendo usada, verificar a ação escolhida pelo usuário
        acao = request.POST.get('acao')

        if acao == 'cancelar':
            messages.info(request, 'Exclusão cancelada.')
            return redirect('mola-detail', pk=mola.id)

        elif acao == 'inativar':
            # Marcar a mola como inativa (soft delete)
            mola.ativo = False
            mola.save()
            messages.success(request, 'Mola marcada como inativa com sucesso!')
            return redirect('mola-list')

        elif acao == 'forcar':
            try:
                # Forçar exclusão (isso vai falhar se houver restrições de chave estrangeira)
                mola.delete()
                messages.success(request, 'Mola excluída com sucesso!')
                return redirect('mola-list')
            except Exception as e:
                messages.error(request, f'Não foi possível excluir a mola: {str(e)}')
                return redirect('mola-detail', pk=mola.id)

        # Se nenhuma ação válida foi escolhida, voltar para a página de confirmação
        messages.error(request, 'Ação inválida.')
        return self.get(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Mola excluída com sucesso!')
        return super().delete(request, *args, **kwargs)


# Views para Materiais Padrão
class MaterialPadraoListView(ListView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_list.html'
    context_object_name = 'materiais_padrao'
    paginate_by = 10

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class MaterialPadraoDetailView(DetailView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_detail.html'
    context_object_name = 'material_padrao'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material_padrao = self.get_object()

        # Materiais que utilizam este material padrão
        context['materiais'] = material_padrao.materiais.all()

        return context


class MaterialPadraoCreateView(CreateView):
    model = MaterialPadrao
    form_class = MaterialPadraoForm
    template_name = 'estoque/material_padrao_form.html'
    success_url = reverse_lazy('material-padrao-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material padrão cadastrado com sucesso!')
        return super().form_valid(form)


class MaterialPadraoUpdateView(UpdateView):
    model = MaterialPadrao
    form_class = MaterialPadraoForm
    template_name = 'estoque/material_padrao_form.html'
    success_url = reverse_lazy('material-padrao-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material padrão atualizado com sucesso!')
        return super().form_valid(form)


class MaterialPadraoDeleteView(DeleteView):
    model = MaterialPadrao
    template_name = 'estoque/material_padrao_confirm_delete.html'
    success_url = reverse_lazy('material-padrao-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material_padrao = self.get_object()

        # Verificar se o material padrão está sendo usado em materiais
        materiais = material_padrao.materiais.all()
        context['materiais'] = materiais
        context['tem_materiais'] = materiais.exists()
        context['total_materiais'] = materiais.count()

        return context

    def post(self, request, *args, **kwargs):
        material_padrao = self.get_object()
        materiais = material_padrao.materiais.all()

        # Se o material padrão está sendo usado em materiais
        if materiais.exists():
            messages.error(request, f'Não é possível excluir o material padrão "{material_padrao}" pois está sendo usado em {materiais.count()} materiais.')
            return redirect('material-padrao-list')

        messages.success(request, f'Material padrão "{material_padrao}" excluído com sucesso!')
        return super().post(request, *args, **kwargs)


# Views para Materiais
class MaterialListView(ListView):
    model = Material
    template_name = 'estoque/material_list.html'
    context_object_name = 'materiais'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = MaterialFilter(self.request.GET, queryset=queryset)

        # Obter os resultados filtrados
        materiais_filtrados = list(self.filterset.qs)

        # Ordenar materiais por nome e diâmetro numérico
        materiais_ordenados = ordenar_materiais(materiais_filtrados)

        return materiais_ordenados

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset

        # Ordenar materiais padrão por nome
        materiais_padrao = MaterialPadrao.objects.all()
        context['materiais_padrao'] = sorted(materiais_padrao, key=lambda mp: mp.nome)

        return context


class MaterialDetailView(DetailView):
    model = Material
    template_name = 'estoque/material_detail.html'
    context_object_name = 'material'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material = self.get_object()

        # Histórico de movimentações
        context['movimentacoes'] = material.movimentacoes.all().order_by('-data')[:10]

        # Molas que utilizam este material
        context['molas'] = material.molas.all()

        return context


class MaterialCreateView(CreateView):
    model = Material
    form_class = MaterialForm
    template_name = 'estoque/material_form.html'
    success_url = reverse_lazy('material-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material cadastrado com sucesso!')
        return super().form_valid(form)


class MaterialUpdateView(UpdateView):
    model = Material
    form_class = MaterialForm
    template_name = 'estoque/material_form.html'
    success_url = reverse_lazy('material-list')

    def form_valid(self, form):
        messages.success(self.request, 'Material atualizado com sucesso!')
        return super().form_valid(form)


class MaterialDeleteView(DeleteView):
    model = Material
    template_name = 'estoque/material_confirm_delete.html'
    success_url = reverse_lazy('material-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        material = self.get_object()

        # Verificar se o material está sendo usado em molas
        molas = material.molas.all()
        context['molas'] = molas
        context['tem_molas'] = molas.exists()
        context['total_molas'] = molas.count()

        return context

    def post(self, request, *args, **kwargs):
        material = self.get_object()
        molas = material.molas.all()

        # Se o material está sendo usado em molas
        if molas.exists():
            acao = request.POST.get('acao')

            # Inativar o material em vez de excluí-lo
            if acao == 'inativar':
                material.ativo = False
                material.save()
                messages.success(request, f'Material "{material}" foi inativado com sucesso!')
                return redirect('material-list')

            # Tentar forçar a exclusão (provavelmente falhará devido à proteção da chave estrangeira)
            elif acao == 'forcar':
                try:
                    return super().post(request, *args, **kwargs)
                except Exception as e:
                    messages.error(request, f'Não foi possível excluir o material: {str(e)}')
                    return redirect('material-detail', pk=material.pk)

            # Se nenhuma ação foi especificada, redirecionar de volta para a página de confirmação
            else:
                messages.warning(request, 'Selecione uma ação para continuar.')
                return redirect('material-delete', pk=material.pk)

        # Se o material não está sendo usado, prosseguir com a exclusão normal
        return super().post(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Material excluído com sucesso!')
        return super().delete(request, *args, **kwargs)


# Views para Movimentações de Estoque
class MovimentacaoEstoqueListView(ListView):
    model = MovimentacaoEstoque
    template_name = 'estoque/movimentacao_estoque_list.html'
    context_object_name = 'movimentacoes'
    paginate_by = 10

    def get_queryset(self):
        # Forçar consulta atualizada sem cache
        queryset = MovimentacaoEstoque.objects.all().select_related('mola')
        self.filterset = MovimentacaoEstoqueFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        # Adicionar timestamp para mostrar quando os dados foram atualizados
        context['timestamp'] = timezone.now()
        return context

    def dispatch(self, request, *args, **kwargs):
        # Desativar cache para esta view
        response = super().dispatch(request, *args, **kwargs)
        response['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response['Pragma'] = 'no-cache'
        response['Expires'] = '0'
        return response


class MovimentacaoEstoqueCreateView(CreateView):
    model = MovimentacaoEstoque
    form_class = MovimentacaoEstoqueForm
    template_name = 'estoque/movimentacao_estoque_form.html'
    success_url = reverse_lazy('movimentacao-estoque-list')

    def get_initial(self):
        """Inicializa o formulário com os parâmetros da URL"""
        initial = super().get_initial()

        # Verificar se há parâmetros na URL
        if 'mola' in self.request.GET:
            initial['mola'] = self.request.GET.get('mola')

        if 'tipo' in self.request.GET:
            initial['tipo'] = self.request.GET.get('tipo')

        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Movimentação registrada com sucesso!')

        # Limpar cache explicitamente
        cache.delete('dashboard_data')
        cache.delete('mola_list_queryset')

        return response


def movimentacao_multipla(request):
    """View para registrar entrada ou saída de múltiplas molas de um mesmo cliente"""
    if request.method == 'POST':
        form = MovimentacaoMultiplaForm(request.POST)
        if form.is_valid():
            cliente = form.cleaned_data['cliente']
            tipo = form.cleaned_data['tipo']
            ordem_venda = form.cleaned_data['ordem_venda']
            observacao = form.cleaned_data['observacao']

            # Processar os campos dinâmicos (molas e quantidades)
            molas_processadas = 0
            erros = []

            # Iterar sobre os pares de nome_mola e quantidade
            for key, value in request.POST.items():
                if key.startswith('nome_mola_') and value:
                    # Extrair o índice do campo
                    idx = key.split('_')[-1]
                    nome_mola = value
                    quantidade_key = f'quantidade_{idx}'

                    # Verificar se existe o campo de quantidade correspondente
                    if quantidade_key in request.POST and request.POST[quantidade_key]:
                        try:
                            quantidade = int(request.POST[quantidade_key])

                            # Buscar a mola pelo nome e cliente
                            mola = Mola.buscar_por_nome_e_cliente(nome_mola, cliente)

                            # Se não encontrar pelo nome_mola, tentar buscar pelo código
                            if not mola:
                                # Tentar encontrar molas que contenham o nome_mola no código
                                molas_possiveis = Mola.objects.filter(codigo__contains=nome_mola, cliente=cliente)
                                if molas_possiveis.count() == 1:
                                    mola = molas_possiveis.first()

                            if mola:
                                # Verificar estoque para saídas
                                if tipo == 'S' and quantidade > mola.quantidade_estoque:
                                    erros.append(f"Mola {mola.codigo}: Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")
                                    continue

                                # Criar a movimentação
                                MovimentacaoEstoque.objects.create(
                                    mola=mola,
                                    tipo=tipo,
                                    quantidade=quantidade,
                                    ordem_venda=ordem_venda,
                                    observacao=observacao
                                )
                                molas_processadas += 1
                            else:
                                erros.append(f"Mola com nome '{nome_mola}' não encontrada para o cliente {cliente}")
                        except ValueError:
                            erros.append(f"Quantidade inválida para a mola {nome_mola}")

            # Exibir mensagens de sucesso e erro
            if molas_processadas > 0:
                messages.success(request, f'{molas_processadas} movimentações registradas com sucesso!')

                # Limpar cache explicitamente para garantir que as movimentações apareçam imediatamente
                cache.delete('dashboard_data')
                cache.delete('mola_list_queryset')

            for erro in erros:
                messages.error(request, erro)

            if molas_processadas > 0:
                return redirect('movimentacao-estoque-list')
    else:
        # Pré-selecionar o tipo se fornecido na URL
        initial = {}
        if 'tipo' in request.GET:
            initial['tipo'] = request.GET.get('tipo')

        form = MovimentacaoMultiplaForm(initial=initial)

    # Obter lista de clientes para o dropdown
    clientes = Mola.objects.values_list('cliente', flat=True).distinct()

    return render(request, 'estoque/movimentacao_multipla_form.html', {
        'form': form,
        'clientes': clientes
    })


# Views para Movimentações de Material
class MovimentacaoMaterialListView(ListView):
    model = MovimentacaoMaterial
    template_name = 'estoque/movimentacao_material_list.html'
    context_object_name = 'movimentacoes'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = MovimentacaoMaterialFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        return context


class MovimentacaoMaterialCreateView(CreateView):
    model = MovimentacaoMaterial
    form_class = MovimentacaoMaterialForm
    template_name = 'estoque/movimentacao_material_form.html'
    success_url = reverse_lazy('movimentacao-material-list')

    def get_initial(self):
        """Inicializa o formulário com os parâmetros da URL"""
        initial = super().get_initial()

        # Verificar se há parâmetros na URL
        if 'material' in self.request.GET:
            initial['material'] = self.request.GET.get('material')

        if 'tipo' in self.request.GET:
            initial['tipo'] = self.request.GET.get('tipo')

        return initial

    def form_valid(self, form):
        messages.success(self.request, 'Movimentação registrada com sucesso!')
        return super().form_valid(form)


# Views para Pedidos de Venda
class PedidoVendaListView(ListView):
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_list.html'
    context_object_name = 'pedidos'
    paginate_by = 10

    def get_queryset(self):
        queryset = super().get_queryset()
        self.filterset = PedidoVendaFilter(self.request.GET, queryset=queryset)
        return self.filterset.qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter'] = self.filterset
        return context


class PedidoVendaDetailView(DetailView):
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_detail.html'
    context_object_name = 'pedido'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        pedido = self.get_object()

        # Itens do pedido
        context['itens'] = pedido.itens.all()

        return context


class PedidoVendaCreateView(CreateView):
    model = PedidoVenda
    form_class = PedidoVendaForm
    template_name = 'estoque/pedido_venda_form.html'
    success_url = reverse_lazy('pedido-venda-list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Pedido cadastrado com sucesso!')

        # Sempre redireciona para adicionar itens ao pedido
        return redirect('pedido-venda-add-item-multiplo', pk=self.object.pk)


class PedidoVendaUpdateView(UpdateView):
    model = PedidoVenda
    form_class = PedidoVendaForm
    template_name = 'estoque/pedido_venda_form.html'
    success_url = reverse_lazy('pedido-venda-list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Pedido atualizado com sucesso!')

        # Sempre redireciona para adicionar itens ao pedido
        return redirect('pedido-venda-add-item-multiplo', pk=self.object.pk)


def pedido_venda_add_item(request, pk):
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    # Redirecionar para a versão de adição múltipla
    return redirect('pedido-venda-add-item-multiplo', pk=pedido.pk)


def pedido_venda_add_item_multiplo(request, pk):
    """View para adicionar múltiplos itens ao pedido de uma vez"""
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if request.method == 'POST':
        # Processar os campos dinâmicos (molas e quantidades)
        itens_processados = 0
        erros = []

        # Verificar se deve movimentar estoque para todos os itens
        movimentar_estoque_geral = request.POST.get('movimentar_estoque_geral') == 'on'

        # Se não deve movimentar estoque, adiciona observação ao pedido
        if not movimentar_estoque_geral:
            if pedido.observacao:
                if "Sem movimentação no estoque" not in pedido.observacao:
                    pedido.observacao += "\nSem movimentação no estoque"
                    pedido.save(update_fields=['observacao'])
            else:
                pedido.observacao = "Sem movimentação no estoque"
                pedido.save(update_fields=['observacao'])

        # Iterar sobre os pares de campos nome_mola_X e quantidade_X
        i = 1
        while f'nome_mola_{i}' in request.POST:
            nome_mola = request.POST.get(f'nome_mola_{i}')
            quantidade_str = request.POST.get(f'quantidade_{i}')

            if nome_mola and quantidade_str:
                try:
                    # Buscar a mola pelo código (nome_mola)
                    # Melhorar a lógica de busca para evitar MultipleObjectsReturned
                    mola = None

                    # 1. Primeiro tentamos buscar pelo código exato
                    try:
                        mola = Mola.objects.get(codigo=nome_mola, ativo=True)
                    except Mola.DoesNotExist:
                        # 2. Se não encontrar, tentamos buscar pelo nome_mola (número após a barra)
                        try:
                            mola = Mola.objects.get(nome_mola=nome_mola, ativo=True)
                        except (Mola.DoesNotExist, Mola.MultipleObjectsReturned):
                            # 3. Se ainda não encontrar ou houver múltiplos, tentamos buscar por código que termine com o nome_mola
                            molas_encontradas = Mola.objects.filter(
                                codigo__endswith=nome_mola,
                                ativo=True
                            ).order_by('codigo')

                            if molas_encontradas.count() == 1:
                                mola = molas_encontradas.first()
                            elif molas_encontradas.count() > 1:
                                # Se houver múltiplas molas, listar as opções
                                codigos = [m.codigo for m in molas_encontradas[:5]]  # Limitar a 5 para não poluir
                                erros.append(f"Múltiplas molas encontradas para '{nome_mola}': {', '.join(codigos)}. Use o código completo.")
                                i += 1
                                continue
                    except Mola.MultipleObjectsReturned:
                        # Se houver múltiplas molas com o mesmo código (não deveria acontecer devido ao unique=True)
                        erros.append(f"Erro interno: múltiplas molas com código '{nome_mola}'. Contate o administrador.")
                        i += 1
                        continue

                    if not mola:
                        erros.append(f"Mola '{nome_mola}' não encontrada.")
                        i += 1
                        continue

                    quantidade = int(quantidade_str)

                    # Usar a configuração geral de movimentação de estoque
                    movimentar_estoque = movimentar_estoque_geral

                    if quantidade <= 0:
                        erros.append(f"Mola {mola.codigo}: A quantidade deve ser maior que zero.")
                    elif movimentar_estoque and quantidade > mola.quantidade_estoque:
                        erros.append(f"Mola {mola.codigo}: Quantidade insuficiente em estoque. Disponível: {mola.quantidade_estoque}")
                    else:
                        # Criar o item do pedido
                        item = ItemPedido(
                            pedido=pedido,
                            mola=mola,
                            quantidade=quantidade,
                            movimentar_estoque=movimentar_estoque
                        )
                        item.save()

                        # Se o pedido estiver aprovado, processa a saída do estoque
                        if pedido.status == 'A':
                            item.processar_saida()

                        itens_processados += 1
                except ValueError:
                    erros.append(f"Quantidade inválida para a mola {nome_mola}.")

            i += 1

        # Exibir mensagens de sucesso e erro
        if itens_processados > 0:
            messages.success(request, f'{itens_processados} itens adicionados com sucesso!')

        for erro in erros:
            messages.error(request, erro)

        if itens_processados > 0:
            return redirect('pedido-venda-detail', pk=pedido.pk)

    return render(request, 'estoque/item_pedido_multiplo_form.html', {
        'pedido': pedido
    })


def pedido_venda_processar(request, pk):
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if pedido.status != 'A':
        pedido.status = 'A'
        pedido.save()

        # Processa todos os itens do pedido
        itens_processados = 0
        for item in pedido.itens.filter(atendido=False):
            if item.processar_saida():
                itens_processados += 1

        if itens_processados > 0:
            messages.success(request, f'{itens_processados} itens processados com sucesso!')
        else:
            messages.warning(request, 'Nenhum item foi processado. Verifique o estoque.')
    else:
        messages.info(request, 'Este pedido já foi processado.')

    return redirect('pedido-venda-detail', pk=pedido.pk)


def pedido_venda_cancelar(request, pk):
    """View para cancelar um pedido de venda"""
    pedido = get_object_or_404(PedidoVenda, pk=pk)

    if request.method == 'POST':
        # Atualiza o status do pedido para cancelado
        pedido.status = 'C'
        pedido.save()

        messages.success(request, f'Pedido {pedido.numero_pedido} cancelado com sucesso!')
        return redirect('pedido-venda-list')

    # Renderiza a página de confirmação
    return render(request, 'estoque/pedido_venda_cancelar.html', {
        'pedido': pedido
    })


class PedidoVendaDeleteView(DeleteView):
    """View para excluir um pedido de venda"""
    model = PedidoVenda
    template_name = 'estoque/pedido_venda_confirm_delete.html'
    success_url = reverse_lazy('pedido-venda-list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Garantir que o objeto pedido esteja disponível no contexto
        context['pedido'] = self.get_object()
        return context

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Pedido excluído com sucesso!')
        return super().delete(request, *args, **kwargs)


class ItemPedidoDeleteView(DeleteView):
    """View para excluir um item de pedido"""
    model = ItemPedido
    template_name = 'estoque/item_pedido_confirm_delete.html'

    def get_success_url(self):
        return reverse_lazy('pedido-venda-detail', kwargs={'pk': self.object.pedido.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['item'] = self.get_object()
        context['pedido'] = self.object.pedido
        return context

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Item removido do pedido com sucesso!')
        return super().delete(request, *args, **kwargs)







# Views para Relatórios
def materiais_json(request):
    """View para retornar todos os materiais em formato JSON"""
    # Filtrar apenas materiais ativos
    materiais = Material.objects.filter(ativo=True)

    # Aplicar filtros se fornecidos
    material_padrao_id = request.GET.get('material_padrao')
    if material_padrao_id:
        try:
            materiais = materiais.filter(material_padrao_id=material_padrao_id)
        except (ValueError, TypeError):
            pass

    # Formatar para JSON
    materiais_json = []
    for material in materiais:
        materiais_json.append({
            'id': material.id,
            'nome': material.nome,
            'diametro': material.diametro,
            'material_padrao_id': material.material_padrao_id if material.material_padrao else None,
            'material_padrao_nome': str(material.material_padrao) if material.material_padrao else None,
            'quantidade_estoque': float(material.quantidade_estoque)
        })

    # Ordenar materiais por nome e diâmetro numérico
    materiais_json = ordenar_materiais(materiais_json)

    return JsonResponse(materiais_json, safe=False)


def relatorio_molas_mais_vendidas(request):
    if request.method == 'POST':
        form = RelatorioMolasForm(request.POST)
        if form.is_valid():
            periodo = form.cleaned_data.get('periodo')
            data_inicial = form.cleaned_data.get('data_inicial')
            data_final = form.cleaned_data.get('data_final')
            subtitulo_personalizado = form.cleaned_data.get('subtitulo_personalizado')
            cliente = form.cleaned_data.get('cliente')
            limite = form.cleaned_data.get('limite') or 10

            # Formato de saída
            formato = request.POST.get('formato', 'html')

            # Verificar se é o caso de meses específicos
            if periodo == 'meses_especificos':
                meses_selecionados = form.cleaned_data.get('meses_selecionados')
                ano_selecionado = form.cleaned_data.get('ano_selecionado')

                if not meses_selecionados:
                    messages.error(request, 'Selecione pelo menos um mês para gerar o relatório.')
                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {'form': form})

                # Gerar múltiplos relatórios, um para cada mês selecionado
                if formato == 'pdf':
                    # Criar um arquivo PDF para cada mês selecionado
                    import io
                    from PyPDF2 import PdfMerger

                    # Criar um merger para juntar todos os PDFs
                    merger = PdfMerger()

                    for mes in meses_selecionados:
                        # Converter mês e ano para datas de início e fim
                        mes_int = int(mes)
                        ano_int = int(ano_selecionado)

                        # Primeiro dia do mês
                        primeiro_dia = date(ano_int, mes_int, 1)

                        # Último dia do mês (calculando o primeiro dia do próximo mês e subtraindo 1 dia)
                        if mes_int == 12:
                            ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                        else:
                            ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                        # Obter nome do mês para o título
                        nome_mes = dict(form.MESES_CHOICES).get(mes)
                        mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                        # Obter dados para este mês específico
                        resultado_mes = Mola.mais_vendidas(
                            periodo='personalizado',
                            data_inicial=primeiro_dia,
                            data_final=ultimo_dia
                        )

                        # Filtra por cliente se especificado
                        if cliente:
                            resultado_mes = [item for item in resultado_mes if cliente.lower() in item['mola_cliente'].lower()]

                        # Limita o número de resultados
                        resultado_mes = resultado_mes[:limite]

                        # Gerar PDF para este mês
                        pdf_buffer = io.BytesIO()
                        gerar_pdf_molas_mais_vendidas(
                            resultado_mes,
                            'personalizado',
                            mes_periodo_texto,
                            primeiro_dia,
                            ultimo_dia,
                            output_buffer=pdf_buffer
                        )

                        # Adicionar ao merger
                        pdf_buffer.seek(0)
                        merger.append(pdf_buffer)

                    # Criar resposta HTTP para o PDF combinado
                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = f'attachment; filename="molas_mais_vendidas_meses_especificos_{ano_selecionado}.pdf"'

                    # Escrever o PDF combinado na resposta
                    merger.write(response)
                    merger.close()

                    return response

                elif formato == 'csv':
                    # Criar um arquivo CSV para cada mês selecionado
                    import io
                    import zipfile

                    # Criar um buffer para o arquivo ZIP
                    zip_buffer = io.BytesIO()

                    # Criar um arquivo ZIP
                    with zipfile.ZipFile(zip_buffer, 'a', zipfile.ZIP_DEFLATED, False) as zip_file:
                        for mes in meses_selecionados:
                            # Converter mês e ano para datas de início e fim
                            mes_int = int(mes)
                            ano_int = int(ano_selecionado)

                            # Primeiro dia do mês
                            primeiro_dia = date(ano_int, mes_int, 1)

                            # Último dia do mês
                            if mes_int == 12:
                                ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                            else:
                                ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                            # Obter nome do mês para o título
                            nome_mes = dict(form.MESES_CHOICES).get(mes)
                            mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                            # Obter dados para este mês específico
                            resultado_mes = Mola.mais_vendidas(
                                periodo='personalizado',
                                data_inicial=primeiro_dia,
                                data_final=ultimo_dia
                            )

                            # Filtra por cliente se especificado
                            if cliente:
                                resultado_mes = [item for item in resultado_mes if cliente.lower() in item['mola_cliente'].lower()]

                            # Limita o número de resultados
                            resultado_mes = resultado_mes[:limite]

                            # Gerar CSV para este mês
                            csv_buffer = io.StringIO()
                            writer = csv.writer(csv_buffer)

                            # Cabeçalho com informação do período
                            writer.writerow(['Relatório de Molas Mais Vendidas'])
                            writer.writerow([f'Período: {mes_periodo_texto}'])
                            writer.writerow([])  # Linha em branco

                            # Cabeçalho dos dados
                            writer.writerow(['Pos.', 'Código', 'Cliente', 'Qtd. Vendida', 'Média Mensal', 'Variação %'])

                            # Dados
                            for i, item in enumerate(resultado_mes, 1):
                                writer.writerow([
                                    i,
                                    item['mola_codigo'],
                                    item['mola_cliente'],
                                    item['total_vendido'],
                                    item.get('media_mensal', 0),
                                    f"{item.get('variacao_percentual', 0)}%"
                                ])

                            # Adicionar o CSV ao arquivo ZIP
                            nome_arquivo = f"molas_mais_vendidas_{nome_mes.lower()}_{ano_selecionado}.csv"
                            zip_file.writestr(nome_arquivo, csv_buffer.getvalue())

                    # Criar resposta HTTP para o arquivo ZIP
                    response = HttpResponse(content_type='application/zip')
                    response['Content-Disposition'] = f'attachment; filename="molas_mais_vendidas_meses_especificos_{ano_selecionado}.zip"'

                    # Escrever o arquivo ZIP na resposta
                    zip_buffer.seek(0)
                    response.write(zip_buffer.getvalue())

                    return response

                else:
                    # Para visualização na tela, mostrar apenas o primeiro mês selecionado
                    mes = meses_selecionados[0]
                    mes_int = int(mes)
                    ano_int = int(ano_selecionado)

                    # Primeiro dia do mês
                    primeiro_dia = date(ano_int, mes_int, 1)

                    # Último dia do mês
                    if mes_int == 12:
                        ultimo_dia = date(ano_int + 1, 1, 1) - timedelta(days=1)
                    else:
                        ultimo_dia = date(ano_int, mes_int + 1, 1) - timedelta(days=1)

                    # Obter nome do mês para o título
                    nome_mes = dict(form.MESES_CHOICES).get(mes)
                    mes_periodo_texto = f"{nome_mes} de {ano_selecionado}"

                    # Obter dados para este mês específico
                    resultado = Mola.mais_vendidas(
                        periodo='personalizado',
                        data_inicial=primeiro_dia,
                        data_final=ultimo_dia
                    )

                    # Filtra por cliente se especificado
                    if cliente:
                        resultado = [item for item in resultado if cliente.lower() in item['mola_cliente'].lower()]

                    # Limita o número de resultados
                    resultado = resultado[:limite]

                    # Exibe na tela
                    messages.info(request, f'Exibindo dados para {mes_periodo_texto}. Para ver todos os meses selecionados, use os botões de exportação.')

                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {
                        'form': form,
                        'resultado': resultado,
                        'periodo': periodo,
                        'periodo_texto': mes_periodo_texto,
                        'data_inicial': primeiro_dia,
                        'data_final': ultimo_dia
                    })

            else:
                # Processamento normal para outros períodos
                # Obtém as molas mais vendidas
                resultado = Mola.mais_vendidas(
                    periodo=periodo,
                    data_inicial=data_inicial,
                    data_final=data_final
                )

                # Filtra por cliente se especificado
                if cliente:
                    resultado = [item for item in resultado if cliente.lower() in item['mola_cliente'].lower()]

                # Limita o número de resultados
                resultado = resultado[:limite]

                # Define o texto do período para exibição
                if subtitulo_personalizado:
                    # Usa o subtítulo personalizado fornecido pelo usuário
                    periodo_texto = subtitulo_personalizado
                elif periodo == 'personalizado' and data_inicial and data_final:
                    periodo_texto = f"De {data_inicial.strftime('%d/%m/%Y')} a {data_final.strftime('%d/%m/%Y')}"
                else:
                    periodo_texto = dict(form.fields['periodo'].choices).get(periodo, 'Todo o período')

                if formato == 'pdf':
                    # Gera PDF
                    return gerar_pdf_molas_mais_vendidas(resultado, periodo, periodo_texto, data_inicial, data_final)
                elif formato == 'csv':
                    # Gera CSV
                    return gerar_csv_molas_mais_vendidas(resultado, periodo, periodo_texto)
                else:
                    # Exibe na tela
                    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {
                        'form': form,
                        'resultado': resultado,
                        'periodo': periodo,
                        'periodo_texto': periodo_texto,
                        'data_inicial': data_inicial,
                        'data_final': data_final
                    })
    else:
        form = RelatorioMolasForm()

    return render(request, 'estoque/relatorio_molas_mais_vendidas.html', {'form': form})


def gerar_pdf_molas_mais_vendidas(resultado, periodo, periodo_texto=None, data_inicial=None, data_final=None, output_buffer=None):
    """Gera PDF do relatório de molas mais vendidas com design avançado."""
    # Se não for fornecido um buffer de saída, cria uma resposta HTTP
    if output_buffer is None:
        # Cria a resposta HTTP com o tipo de conteúdo PDF
        response = HttpResponse(content_type='application/pdf')

        # Define o nome do arquivo com base no período
        if periodo == 'personalizado' and data_inicial and data_final:
            filename = f"molas_mais_vendidas_{data_inicial.strftime('%Y%m%d')}_a_{data_final.strftime('%Y%m%d')}.pdf"
        else:
            filename = f"molas_mais_vendidas_{periodo or 'todos'}.pdf"

        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # Usa a resposta como buffer de saída
        output = response
    else:
        # Usa o buffer fornecido
        output = output_buffer

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    doc = SimpleDocTemplate(output, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Se não foi fornecido um texto de período, gera um com base no período
    if not periodo_texto:
        periodo_texto = {
            'semana': 'Última Semana',
            'mes': 'Último Mês',
            '3meses': 'Últimos 3 Meses',
            '6meses': 'Últimos 6 Meses',
            'ano': 'Último Ano',
            'personalizado': f"De {data_inicial.strftime('%d/%m/%Y')} a {data_final.strftime('%d/%m/%Y')}",
            None: 'Todo o Período'
        }.get(periodo, 'Todo o Período')

    # Adiciona informações do relatório
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(f"Período: {periodo_texto}", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Adiciona descrição
    elements.append(Paragraph(
        "Este relatório apresenta as molas mais vendidas no período selecionado, "
        "ordenadas por quantidade vendida em ordem decrescente. Inclui média mensal de vendas "
        "e variação percentual em relação ao período anterior.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela com novas colunas
    data = [['Pos.', 'Código', 'Cliente', 'Qtd. Vendida', 'Média Mensal', 'Variação %']]

    for i, item in enumerate(resultado, 1):
        # Formatar a variação percentual com sinal e cor
        variacao = item.get('variacao_percentual', 0)
        if variacao > 0:
            variacao_str = f"+{variacao}%"
        elif variacao < 0:
            variacao_str = f"{variacao}%"
        else:
            variacao_str = "0%"

        data.append([
            str(i),
            item['mola_codigo'],
            item['mola_cliente'],
            str(item['total_vendido']),
            str(item.get('media_mensal', 0)),
            variacao_str
        ])

    # Cria a tabela com larguras ajustadas para acomodar as novas colunas
    table = Table(data, colWidths=[1.5*cm, 3*cm, 6*cm, 2.5*cm, 2.5*cm, 2.5*cm])

    # Aplica estilo avançado à tabela
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona alinhamento específico para algumas colunas
    table_style = TableStyle([
        ('ALIGN', (0, 1), (0, -1), 'CENTER'),  # Centraliza a coluna de posição
        ('ALIGN', (3, 1), (5, -1), 'RIGHT'),   # Alinha à direita as colunas numéricas
    ])

    # Adiciona cores para a variação percentual
    for i, item in enumerate(resultado, 1):
        variacao = item.get('variacao_percentual', 0)
        if variacao > 0:
            # Verde para variação positiva
            table_style.add('TEXTCOLOR', (5, i), (5, i), colors.green)
        elif variacao < 0:
            # Vermelho para variação negativa
            table_style.add('TEXTCOLOR', (5, i), (5, i), colors.red)

    table.setStyle(table_style)

    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))

    # Adiciona resumo
    elements.append(Paragraph(f"Total de itens: {len(resultado)}", styles['info']))

    # Adiciona gráfico de tendência de vendas mensais
    elements.append(Spacer(1, 1*cm))
    elements.append(Paragraph("Tendência de Vendas Totais por Mês", styles['subtitle']))
    elements.append(Spacer(1, 0.5*cm))

    # Obter dados de vendas mensais para o gráfico de tendência
    vendas_mensais = Mola.obter_vendas_mensais(periodo, data_inicial, data_final)

    if vendas_mensais['labels'] and vendas_mensais['valores']:
        # Criar gráfico de linha para tendência de vendas
        trend_chart = create_line_chart(
            vendas_mensais['valores'],
            vendas_mensais['labels'],
            "Tendência de Vendas Mensais",
            xlabel="Mês/Ano",
            ylabel="Quantidade Vendida"
        )
        elements.append(trend_chart)
    else:
        elements.append(Paragraph("Dados insuficientes para gerar o gráfico de tendência.", styles['info']))

    # Se houver dados suficientes, adiciona um gráfico de barras das molas mais vendidas
    if len(resultado) > 1:
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph("Gráfico de Vendas por Mola", styles['subtitle']))
        elements.append(Spacer(1, 0.5*cm))

        # Prepara dados para o gráfico
        labels = [item['mola_codigo'] for item in resultado[:10]]  # Limita a 10 itens para o gráfico
        dados = [item['total_vendido'] for item in resultado[:10]]

        # Cria o gráfico
        chart = create_chart(
            dados,
            labels,
            "Top Molas - Quantidade Vendida",
            xlabel="Código da Mola",
            ylabel="Quantidade"
        )
        elements.append(chart)

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Relatório de Molas Mais Vendidas"
        filtros = f"Período: {periodo_texto}"
        add_header_footer(canvas, doc, titulo, filtros)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    # Se estamos usando um buffer de saída personalizado, retorna None
    # Se estamos usando uma resposta HTTP, retorna a resposta
    if output_buffer is not None:
        return None
    else:
        return output


def gerar_csv_molas_mais_vendidas(resultado, periodo, periodo_texto=None):
    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')

    # Define o nome do arquivo
    if periodo == 'personalizado':
        # Extrai as datas do texto do período se disponível
        if periodo_texto and 'De ' in periodo_texto:
            periodo_para_arquivo = periodo_texto.replace('/', '').replace(' ', '_')
            filename = f"molas_mais_vendidas_{periodo_para_arquivo}.csv"
        else:
            filename = f"molas_mais_vendidas_personalizado.csv"
    else:
        filename = f"molas_mais_vendidas_{periodo or 'todos'}.csv"

    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho com informação do período
    writer.writerow(['Relatório de Molas Mais Vendidas'])
    writer.writerow([f'Período: {periodo_texto or "Todo o período"}'])
    writer.writerow([])  # Linha em branco

    # Cabeçalho dos dados
    writer.writerow(['Pos.', 'Código', 'Cliente', 'Qtd. Vendida'])

    # Dados
    for i, item in enumerate(resultado, 1):
        writer.writerow([
            i,
            item['mola_codigo'],
            item['mola_cliente'],
            item['total_vendido']
        ])

    return response


# API para obter dados de vendas mensais para o gráfico de tendência
def vendas_mensais_json(request):
    """API para obter dados de vendas mensais para o gráfico de tendência"""
    try:
        # Obter parâmetros da requisição
        periodo = request.GET.get('periodo')
        data_inicial_str = request.GET.get('data_inicial')
        data_final_str = request.GET.get('data_final')

        # Converter strings de data para objetos date
        data_inicial = None
        data_final = None

        if data_inicial_str:
            try:
                data_inicial = datetime.strptime(data_inicial_str, '%Y-%m-%d').date()
            except ValueError:
                pass

        if data_final_str:
            try:
                data_final = datetime.strptime(data_final_str, '%Y-%m-%d').date()
            except ValueError:
                pass

        # Obter dados de vendas mensais
        vendas_mensais = Mola.obter_vendas_mensais(periodo, data_inicial, data_final)

        return JsonResponse(vendas_mensais)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# API para obter informações sobre um material específico
def material_api(request, material_id):
    """API para obter informações sobre um material específico"""
    try:
        material = Material.objects.get(pk=material_id)
        data = {
            'id': material.id,
            'nome': material.nome,
            'diametro': material.diametro,
            'material_padrao_id': material.material_padrao_id if material.material_padrao else None,
            'quantidade_estoque': float(material.quantidade_estoque),
            'ativo': material.ativo
        }
        return JsonResponse(data)
    except Material.DoesNotExist:
        return JsonResponse({'error': 'Material não encontrado'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# API para filtrar materiais por material padrão
def filtrar_materiais_api(request):
    """API para filtrar materiais por material padrão"""
    try:
        # Obter o ID do material padrão da query string
        material_padrao_id = request.GET.get('material_padrao')

        if not material_padrao_id:
            return JsonResponse({
                'success': False,
                'error': 'Parâmetro material_padrao é obrigatório'
            }, status=400)

        # Buscar o material padrão
        try:
            material_padrao = MaterialPadrao.objects.get(pk=material_padrao_id)
        except MaterialPadrao.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Material padrão não encontrado'
            }, status=404)

        # Buscar todos os materiais que são variantes desse material padrão
        materiais = Material.objects.filter(
            material_padrao=material_padrao,
            ativo=True
        )

        # Formatar os materiais para retornar no JSON
        materiais_json = []
        for material in materiais:
            materiais_json.append({
                'id': material.id,
                'nome': str(material),
                'diametro': material.diametro,
                'quantidade_estoque': float(material.quantidade_estoque)
            })

        # Ordenar materiais por nome e diâmetro numérico
        materiais_json = ordenar_materiais(materiais_json)

        return JsonResponse({
            'success': True,
            'material_padrao': {
                'id': material_padrao.id,
                'nome': material_padrao.nome,
                'diametro': material_padrao.diametro
            },
            'materiais': materiais_json
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

# Views para Alertas
def alertas_estoque(request):
    # Molas com estoque abaixo do mínimo
    molas_estoque_baixo = Mola.objects.filter(quantidade_estoque__lte=F('estoque_minimo'))

    # Materiais com estoque abaixo do mínimo
    materiais_estoque_baixo = Material.objects.filter(quantidade_estoque__lte=F('estoque_minimo'))

    return render(request, 'estoque/alertas_estoque.html', {
        'molas_estoque_baixo': molas_estoque_baixo,
        'materiais_estoque_baixo': materiais_estoque_baixo
    })





# Views para Previsão de Demanda
class PrevisaoDemandaListView(ListView):
    model = PrevisaoDemanda
    template_name = 'estoque/previsao_demanda_list.html'
    context_object_name = 'previsoes'
    paginate_by = 10
    ordering = ['-data_previsao']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['molas'] = Mola.objects.all()

        # Adicionar informações sobre métodos de previsão avançada disponíveis
        context['advanced_forecasting'] = ADVANCED_FORECASTING_AVAILABLE

        if ADVANCED_FORECASTING_AVAILABLE:
            context['statsmodels_installed'] = STATSMODELS_INSTALLED
            context['prophet_installed'] = PROPHET_INSTALLED
            context['sklearn_installed'] = SKLEARN_INSTALLED

            # Adicionar informações sobre os métodos disponíveis
            available_methods = []
            if STATSMODELS_INSTALLED:
                available_methods.append('ARIMA/SARIMA')
            if PROPHET_INSTALLED:
                available_methods.append('Prophet')
            if SKLEARN_INSTALLED:
                available_methods.append('Machine Learning')

            context['available_methods'] = available_methods

        return context


def gerar_previsao_demanda(request):
    if request.method == 'POST':
        mola_id = request.POST.get('mola_id')
        metodo = request.POST.get('metodo', 'auto')
        periodo = request.POST.get('periodo', 'M')

        if mola_id:
            # Verifica se deve usar previsão avançada ou básica
            if ADVANCED_FORECASTING_AVAILABLE:
                # Mapear método do formulário para método da API de previsão avançada
                metodo_map = {
                    'MM': 'media_movel',
                    'SE': 'arima',
                    'RL': 'prophet',
                    'AI': 'ensemble',
                    'auto': 'auto'
                }

                # Gera a previsão usando o módulo avançado
                previsao = generate_forecast(mola_id, metodo_map.get(metodo, 'auto'), periodo)
                metodo_usado = "avançado"
            else:
                # Fallback para o método básico
                previsao = PrevisaoDemanda.gerar_previsao(mola_id, metodo, periodo)
                metodo_usado = "básico"

            if previsao:
                messages.success(request, f'Previsão gerada com sucesso para {previsao.mola.codigo} usando método {metodo_usado}')

                # Adicionar informações sobre precisão se disponível
                if hasattr(previsao, 'precisao') and previsao.precisao:
                    messages.info(request, f'Precisão estimada: {previsao.precisao:.1f}%')
            else:
                messages.warning(request, 'Não foi possível gerar a previsão. Dados históricos insuficientes.')
        else:
            messages.error(request, 'Selecione uma mola para gerar a previsão.')

    return redirect('previsao-demanda-list')


def gerar_todas_previsoes(request):
    """Gera previsões para todas as molas"""
    # Obter parâmetros
    metodo = request.POST.get('metodo', 'auto')
    periodo = request.POST.get('periodo', 'M')

    # Verifica se deve usar previsão avançada ou básica
    if ADVANCED_FORECASTING_AVAILABLE:
        # Usar o módulo avançado
        previsoes = generate_all_forecasts(metodo, periodo)
        total = len(previsoes)
        metodo_usado = "avançado"

        # Calcular precisão média
        if previsoes:
            precisao_media = sum(p.precisao or 0 for p in previsoes) / total if total > 0 else 0
        else:
            precisao_media = 0
    else:
        # Fallback para o método básico
        molas = Mola.objects.all()
        total = 0
        previsoes = []

        for mola in molas:
            previsao = PrevisaoDemanda.gerar_previsao(mola.id, metodo, periodo)
            if previsao:
                previsoes.append(previsao)
                total += 1

        metodo_usado = "básico"
        precisao_media = 0

    if total > 0:
        messages.success(request, f'Previsões geradas com sucesso para {total} molas usando método {metodo_usado}.')

        # Adicionar informações sobre precisão se disponível
        if precisao_media > 0:
            messages.info(request, f'Precisão média estimada: {precisao_media:.1f}%')
    else:
        messages.warning(request, 'Não foi possível gerar previsões. Dados históricos insuficientes.')

    return redirect('previsao-demanda-list')


class PrevisaoDemandaDetailView(DetailView):
    model = PrevisaoDemanda
    template_name = 'estoque/previsao_demanda_detail.html'
    context_object_name = 'previsao'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        previsao = self.get_object()
        mola = previsao.mola

        # Histórico de vendas
        vendas = MovimentacaoEstoque.objects.filter(
            mola=mola,
            tipo='S'
        ).order_by('-data')[:12]

        # Agrupa por mês para gráfico
        vendas_por_mes = {}
        for venda in vendas:
            mes_ano = venda.data.strftime('%Y-%m')
            if mes_ano not in vendas_por_mes:
                vendas_por_mes[mes_ano] = 0
            vendas_por_mes[mes_ano] += venda.quantidade

        # Converte para formato adequado para gráfico
        labels = []
        dados = []
        for mes, quantidade in sorted(vendas_por_mes.items()):
            ano, mes = mes.split('-')
            labels.append(f"{mes}/{ano}")
            dados.append(quantidade)

        context['vendas'] = vendas
        context['labels'] = json.dumps(labels)
        context['dados'] = json.dumps(dados)
        context['previsao_valor'] = json.dumps([previsao.quantidade_prevista])

        # Adicionar informações sobre métodos de previsão avançada disponíveis
        context['advanced_forecasting'] = ADVANCED_FORECASTING_AVAILABLE

        if ADVANCED_FORECASTING_AVAILABLE:
            context['statsmodels_installed'] = STATSMODELS_INSTALLED
            context['prophet_installed'] = PROPHET_INSTALLED
            context['sklearn_installed'] = SKLEARN_INSTALLED

        return context


# Views para Análise de Obsolescência
class AnaliseObsolescenciaListView(ListView):
    model = AnaliseObsolescencia
    template_name = 'estoque/analise_obsolescencia_list.html'
    context_object_name = 'analises'
    paginate_by = 20  # Aumentado para melhor desempenho
    ordering = ['-data_analise']

    def get_queryset(self):
        # Usar uma subconsulta para obter as análises mais recentes de cada mola
        # Isso é muito mais eficiente que o método anterior
        subquery = AnaliseObsolescencia.objects.filter(
            mola=OuterRef('mola')
        ).order_by('-data_analise').values('id')[:1]

        queryset = AnaliseObsolescencia.objects.filter(
            id__in=Subquery(subquery)
        ).select_related('mola')  # Otimização: carrega dados da mola em uma única consulta

        # Filtra por classificação se especificado
        classificacao = self.request.GET.get('classificacao')
        if classificacao:
            queryset = queryset.filter(classificacao=classificacao)

        return queryset.order_by('-data_analise')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Resumo por classificação - otimizado para uma única consulta
        classificacao_counts = AnaliseObsolescencia.objects.values('classificacao').annotate(
            count=Count('classificacao')
        ).order_by('classificacao')

        # Mapear para o formato esperado
        resumo = {}
        for classificacao, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
            count = 0
            for item in classificacao_counts:
                if item['classificacao'] == classificacao:
                    count = item['count']
                    break

            resumo[classificacao] = {
                'nome': nome,
                'count': count
            }

        context['resumo'] = resumo
        context['classificacao_atual'] = self.request.GET.get('classificacao', '')

        return context


def gerar_analise_obsolescencia(request):
    """Gera análise de obsolescência para todas as molas"""
    resultados = AnaliseObsolescencia.analisar_todos_itens()

    # Conta itens por classificação
    contagem = {}
    for analise in resultados:
        if analise.classificacao not in contagem:
            contagem[analise.classificacao] = 0
        contagem[analise.classificacao] += 1

    # Prepara mensagem de sucesso
    mensagem = f"Análise concluída para {len(resultados)} itens: "
    for classificacao, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
        if classificacao in contagem:
            mensagem += f"{nome}: {contagem[classificacao]}, "

    messages.success(request, mensagem[:-2])  # Remove a última vírgula e espaço

    return redirect('analise-obsolescencia-list')


class AnaliseObsolescenciaDetailView(DetailView):
    model = AnaliseObsolescencia
    template_name = 'estoque/analise_obsolescencia_detail.html'
    context_object_name = 'analise'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        analise = self.get_object()
        mola = analise.mola

        # Histórico de movimentações
        movimentacoes = MovimentacaoEstoque.objects.filter(
            mola=mola
        ).order_by('-data')[:10]

        # Outras análises da mesma mola
        outras_analises = AnaliseObsolescencia.objects.filter(
            mola=mola
        ).exclude(id=analise.id).order_by('-data_analise')[:5]

        context['movimentacoes'] = movimentacoes
        context['outras_analises'] = outras_analises

        return context


# Novas views para relatórios e impressão
def relatorio_estoque(request):
    """View para relatório de estoque personalizado"""
    if request.method == 'POST':
        form = RelatorioEstoqueForm(request.POST)
        if form.is_valid():
            # Obtém os filtros
            cliente = form.cleaned_data.get('cliente')
            codigo = form.cleaned_data.get('codigo')
            estoque_baixo = form.cleaned_data.get('estoque_baixo')
            ordenacao = form.cleaned_data.get('ordenacao') or 'codigo'

            # Filtra as molas
            molas = Mola.objects.all()

            if cliente:
                molas = molas.filter(cliente__icontains=cliente)

            if codigo:
                molas = molas.filter(codigo__icontains=codigo)

            if estoque_baixo:
                molas = molas.filter(quantidade_estoque__lte=F('estoque_minimo'))

            # Ordena os resultados
            molas = molas.order_by(ordenacao)

            # Conta molas com estoque baixo
            estoque_baixo_count = molas.filter(quantidade_estoque__lte=F('estoque_minimo')).count()

            # Formato de saída
            formato = request.POST.get('formato', 'html')

            if formato == 'pdf':
                # Gera PDF
                return gerar_pdf_estoque(molas, cliente, codigo, estoque_baixo)
            elif formato == 'csv':
                # Gera CSV
                return gerar_csv_estoque(molas, cliente, codigo, estoque_baixo)
            else:
                # Exibe na tela
                return render(request, 'estoque/relatorio_estoque.html', {
                    'form': form,
                    'molas': molas,
                    'estoque_baixo_count': estoque_baixo_count,
                    'filtro_cliente': cliente,
                    'filtro_codigo': codigo,
                    'filtro_estoque_baixo': estoque_baixo
                })
        else:
            # Se o formulário não for válido, exibe os erros
            messages.error(request, 'Erro na validação do formulário. Verifique os dados inseridos.')
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = RelatorioEstoqueForm()

    return render(request, 'estoque/relatorio_estoque.html', {'form': form})


def gerar_pdf_estoque(molas, cliente=None, codigo=None, estoque_baixo=False):
    """Gera PDF do relatório de estoque com design avançado."""
    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="relatorio_estoque.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    # Alterado para orientação vertical (portrait) para padronizar todos os relatórios
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Prepara informações de filtro para o cabeçalho
    filtros_texto = []
    if cliente:
        filtros_texto.append(f"Cliente: {cliente}")
    if codigo:
        filtros_texto.append(f"Código: {codigo}")
    if estoque_baixo:
        filtros_texto.append("Apenas Estoque Baixo")

    filtros_str = " | ".join(filtros_texto) if filtros_texto else "Sem filtros aplicados"

    # Adiciona descrição
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(
        "Este relatório apresenta a situação atual do estoque de molas, "
        "incluindo informações sobre quantidade disponível e status.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Dados da tabela
    data = [['Código', 'Cliente', 'Material', 'Diâmetro', 'Est. Atual', 'Est. Mín.', 'Status']]

    # Contadores para estatísticas
    total_molas = len(molas)
    estoque_baixo_count = 0
    estoque_total = 0

    for mola in molas:
        try:
            # Verifica status de estoque
            if mola.quantidade_estoque <= mola.estoque_minimo:
                status = "Estoque Baixo"
                estoque_baixo_count += 1
            else:
                status = "OK"

            estoque_total += mola.quantidade_estoque

            # Tratamento seguro para material
            try:
                material = mola.material.nome if mola.material else "-"
            except:
                material = "-"

            # Tratamento seguro para diâmetro
            try:
                if mola.diametro:
                    diametro = str(mola.diametro)
                elif mola.material and mola.material.diametro:
                    diametro = str(mola.material.diametro)
                else:
                    diametro = "-"
            except:
                diametro = "-"

            data.append([
                mola.codigo or "Sem código",
                mola.cliente or "Sem cliente",
                material,
                diametro,
                str(mola.quantidade_estoque),
                str(mola.estoque_minimo),
                status
            ])
        except Exception as e:
            # Em caso de erro, adiciona uma linha com informação parcial
            data.append([
                getattr(mola, 'codigo', 'Erro'),
                getattr(mola, 'cliente', 'Erro'),
                "Erro",
                "Erro",
                "Erro",
                "Erro",
                "Erro"
            ])

    # Cria a tabela com larguras ajustadas para orientação vertical
    table = Table(data, colWidths=[3*cm, 4*cm, 3*cm, 2*cm, 2*cm, 2*cm, 2.5*cm])

    # Aplica estilo avançado à tabela
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona alinhamento específico para algumas colunas
    table_style = TableStyle([
        ('ALIGN', (4, 1), (6, -1), 'CENTER'),  # Centraliza colunas de estoque e status
    ])

    # Adiciona formatação condicional para status
    for i in range(1, len(data)):
        status = data[i][-1]
        if status == "Estoque Baixo":
            table_style.add('TEXTCOLOR', (6, i), (6, i), colors.red)
            table_style.add('FONTNAME', (6, i), (6, i), 'Helvetica-Bold')

    table.setStyle(table_style)
    elements.append(table)

    # Adiciona resumo e estatísticas
    elements.append(Spacer(1, 0.8*cm))
    elements.append(Paragraph("Resumo do Estoque", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Cria tabela de resumo
    resumo_data = [
        ["Total de Molas", str(total_molas)],
        ["Molas com Estoque Baixo", f"{estoque_baixo_count} ({estoque_baixo_count/total_molas*100:.1f}% do total)" if total_molas > 0 else "0"],
        ["Quantidade Total em Estoque", str(estoque_total)],
        ["Média por Item", f"{estoque_total/total_molas:.1f}" if total_molas > 0 else "0"]
    ]

    resumo_table = Table(resumo_data, colWidths=[8*cm, 8*cm])
    resumo_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
    ]))

    elements.append(resumo_table)

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Relatório de Estoque"
        add_header_footer(canvas, doc, titulo, filtros_str)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def gerar_csv_estoque(molas, cliente=None, codigo=None, estoque_baixo=False):
    """Gera CSV do relatório de estoque"""
    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="relatorio_estoque.csv"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho
    writer.writerow(['Código', 'Cliente', 'Material', 'Diâmetro', 'Est. Atual', 'Est. Mín.', 'Status'])

    # Dados
    for mola in molas:
        status = "Estoque Baixo" if mola.quantidade_estoque <= mola.estoque_minimo else "OK"
        material = mola.material.nome if mola.material else "-"
        diametro = mola.diametro or (mola.material.diametro if mola.material else "-")

        writer.writerow([
            mola.codigo,
            mola.cliente,
            material,
            diametro,
            mola.quantidade_estoque,
            mola.estoque_minimo,
            status
        ])

    return response


def previsao_demanda_pdf(request, pk):
    """Gera PDF da previsão de demanda com design avançado."""
    previsao = get_object_or_404(PrevisaoDemanda, pk=pk)
    mola = previsao.mola

    # Obtém dados para o gráfico
    vendas = MovimentacaoEstoque.objects.filter(
        mola=mola,
        tipo='S'
    ).order_by('-data')[:12]

    # Agrupa por mês para gráfico
    vendas_por_mes = {}
    for venda in vendas:
        mes_ano = venda.data.strftime('%Y-%m')
        if mes_ano not in vendas_por_mes:
            vendas_por_mes[mes_ano] = 0
        vendas_por_mes[mes_ano] += venda.quantidade

    # Converte para formato adequado para gráfico
    labels = []
    dados = []
    for mes, quantidade in sorted(vendas_por_mes.items()):
        ano, mes = mes.split('-')
        labels.append(f"{mes}/{ano}")
        dados.append(quantidade)

    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="previsao_demanda_{previsao.id}.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Adiciona informações do relatório
    elements.append(Spacer(1, 0.5*cm))

    # Informações da previsão
    elements.append(Paragraph("Informações da Previsão", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    info_data = [
        ['Mola:', mola.codigo],
        ['Cliente:', mola.cliente],
        ['Data da Previsão:', previsao.data_previsao.strftime('%d/%m/%Y')],
        ['Período:', previsao.get_periodo_display()],
        ['Método:', previsao.get_metodo_display()],
        ['Quantidade Prevista:', str(previsao.quantidade_prevista)],
        ['Validade:', f"{previsao.data_inicio.strftime('%d/%m/%Y')} a {previsao.data_fim.strftime('%d/%m/%Y')}"]
    ]

    if previsao.precisao:
        info_data.append(['Precisão:', f"{previsao.precisao:.2f}%"])

    info_table = Table(info_data, colWidths=[5*cm, 12*cm])
    info_table.setStyle(TableStyle([
        ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f0f0f0')),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ('WORDWRAP', (0, 0), (-1, -1), True),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
    ]))

    elements.append(info_table)
    elements.append(Spacer(1, 0.8*cm))

    # Recomendações
    elements.append(Paragraph("Recomendações", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Cria um box de destaque para as recomendações
    recomendacao_texto = []
    recomendacao_texto.append(Paragraph(f"Com base na previsão de demanda de {previsao.quantidade_prevista} unidades para o próximo período:", styles['normal']))
    recomendacao_texto.append(Spacer(1, 0.2*cm))

    if mola.quantidade_estoque < previsao.quantidade_prevista:
        deficit = previsao.quantidade_prevista - mola.quantidade_estoque
        percentual = (deficit / previsao.quantidade_prevista) * 100

        recomendacao_texto.append(Paragraph(f"• O estoque atual de {mola.quantidade_estoque} unidades é <b>insuficiente</b> para atender à demanda prevista.", styles['normal']))
        recomendacao_texto.append(Paragraph(f"• Déficit de {deficit} unidades ({percentual:.1f}% da demanda prevista).", styles['normal']))
        recomendacao_texto.append(Spacer(1, 0.2*cm))
        recomendacao_texto.append(Paragraph(f"• <b>Recomendação:</b> Produzir pelo menos {deficit} unidades adicionais.", styles['highlight']))
    else:
        excesso = mola.quantidade_estoque - previsao.quantidade_prevista
        percentual = (excesso / previsao.quantidade_prevista) * 100 if previsao.quantidade_prevista > 0 else 100

        recomendacao_texto.append(Paragraph(f"• O estoque atual de {mola.quantidade_estoque} unidades é <b>suficiente</b> para atender à demanda prevista.", styles['normal']))
        recomendacao_texto.append(Paragraph(f"• Excesso de {excesso} unidades ({percentual:.1f}% acima da demanda prevista).", styles['normal']))
        recomendacao_texto.append(Spacer(1, 0.2*cm))
        recomendacao_texto.append(Paragraph(f"• <b>Recomendação:</b> Manter o nível de estoque atual.", styles['highlight']))

    # Cria um box para as recomendações
    recomendacao_box = Table([[recomendacao_texto]], colWidths=[17*cm])
    recomendacao_box.setStyle(TableStyle([
        ('BOX', (0, 0), (-1, -1), 1, colors.HexColor('#CCCCCC')),
        ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#F9F9F9')),
        ('LEFTPADDING', (0, 0), (-1, -1), 12),
        ('RIGHTPADDING', (0, 0), (-1, -1), 12),
        ('TOPPADDING', (0, 0), (-1, -1), 12),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
    ]))

    elements.append(recomendacao_box)
    elements.append(Spacer(1, 0.8*cm))

    # Adiciona gráfico se houver dados suficientes
    if len(dados) > 1:
        elements.append(Paragraph("Análise de Vendas e Previsão", styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

        # Cria um gráfico avançado
        plt.figure(figsize=(8, 4))

        # Estilo personalizado
        plt.style.use('seaborn-v0_8-darkgrid')

        # Cores personalizadas
        bar_color = '#bb86fc'  # Roxo/lilás
        line_color = '#cf6679'  # Vermelho/rosa

        # Criar gráfico de barras
        bars = plt.bar(labels, dados, color=bar_color, alpha=0.7)

        # Adicionar linha de previsão
        plt.axhline(y=previsao.quantidade_prevista, color=line_color, linestyle='--', linewidth=2, label='Previsão')

        # Adicionar valor em cima de cada barra
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', fontweight='bold')

        # Adicionar valor da previsão
        plt.text(len(labels) - 0.5, previsao.quantidade_prevista + 0.5,
                f'Previsão: {previsao.quantidade_prevista}',
                color=line_color, fontweight='bold', ha='right')

        # Personalizar o gráfico
        plt.xlabel('Período', fontsize=12)
        plt.ylabel('Quantidade', fontsize=12)
        plt.title('Histórico de Vendas e Previsão de Demanda', fontsize=14, fontweight='bold')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.legend()

        # Adicionar grade
        plt.grid(axis='y', linestyle='--', alpha=0.7)

        # Salva o gráfico em um buffer
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300)
        img_buffer.seek(0)

        # Adiciona o gráfico ao PDF
        img = RLImage(img_buffer, width=16*cm, height=9*cm)
        elements.append(img)
        elements.append(Spacer(1, 0.5*cm))

        # Fecha a figura para liberar memória
        plt.close()

    # Histórico de vendas
    elements.append(Paragraph("Histórico de Movimentações", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    if vendas:
        vendas_data = [['Data', 'Tipo', 'Quantidade', 'Ordem']]

        for venda in vendas:
            tipo = "Entrada" if venda.tipo == 'E' else "Saída"
            vendas_data.append([
                venda.data.strftime('%d/%m/%Y'),
                tipo,
                str(venda.quantidade),
                venda.ordem_venda or "--"
            ])

        vendas_table = Table(vendas_data, colWidths=[4*cm, 3.5*cm, 3.5*cm, 6*cm])
        vendas_table.setStyle(create_table_style(has_header=True, alternating_colors=True))

        elements.append(vendas_table)
    else:
        elements.append(Paragraph("Nenhuma movimentação registrada.", styles['info']))

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = f"Previsão de Demanda - {mola.codigo}"
        filtros = f"Cliente: {mola.cliente} | Método: {previsao.get_metodo_display()}"
        add_header_footer(canvas, doc, titulo, filtros)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def analise_obsolescencia_pdf(request):
    """Gera PDF da análise de obsolescência com design avançado."""
    # Obtém os filtros
    classificacao = request.GET.get('classificacao')

    # Filtra as análises
    analises = AnaliseObsolescencia.objects.all()

    if classificacao:
        analises = analises.filter(classificacao=classificacao)

    # Agrupa por mola (pega apenas a análise mais recente de cada mola)
    molas_ids = analises.values_list('mola_id', flat=True).distinct()
    resultado = []

    for mola_id in molas_ids:
        analise = analises.filter(mola_id=mola_id).order_by('-data_analise').first()
        if analise:
            resultado.append(analise)

    # Cria a resposta HTTP com o tipo de conteúdo PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="analise_obsolescencia.pdf"'

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    # Alterado para formato vertical (retrato)
    doc = SimpleDocTemplate(response, pagesize=A4,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)
    elements = []

    # Obtém estilos personalizados
    styles = get_styles()

    # Prepara informações de filtro para o cabeçalho
    filtro_texto = ""
    if classificacao:
        for c, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
            if c == classificacao:
                filtro_texto = f"Filtro: {nome}"
                break

    # Adiciona descrição
    elements.append(Spacer(1, 0.5*cm))
    elements.append(Paragraph(
        "Este relatório apresenta a análise de obsolescência do estoque, "
        "identificando itens sem movimentação e classificando-os por nível de risco.",
        styles['normal']
    ))
    elements.append(Spacer(1, 0.5*cm))

    # Resumo por classificação
    elements.append(Paragraph("Resumo por Classificação", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Calcula o resumo por classificação
    resumo = {}
    total_itens = 0
    total_valor = 0

    for c, nome in AnaliseObsolescencia.CLASSIFICACAO_CHOICES:
        analises_classe = AnaliseObsolescencia.objects.filter(
            classificacao=c,
            mola_id__in=molas_ids
        ).order_by('-data_analise')

        # Pega apenas a análise mais recente de cada mola
        molas_classe = set()
        analises_unicas = []

        for analise in analises_classe:
            if analise.mola_id not in molas_classe:
                molas_classe.add(analise.mola_id)
                analises_unicas.append(analise)

        count = len(analises_unicas)
        valor = sum(a.valor_estoque for a in analises_unicas)

        total_itens += count
        total_valor += valor

        resumo[c] = {
            'nome': nome,
            'count': count,
            'valor': valor
        }

    # Cria dados para o gráfico
    labels = []
    counts = []
    cores = []

    # Cores para cada classificação
    cores_map = {
        'A': '#bb86fc',  # Roxo/lilás (ativo)
        'B': '#03dac6',  # Turquesa (baixa rotatividade)
        'C': '#ffab40',  # Laranja (crítico)
        'O': '#cf6679',  # Vermelho (obsoleto)
    }

    for c, info in sorted(resumo.items()):
        if info['count'] > 0:
            labels.append(info['nome'])
            counts.append(info['count'])
            cores.append(cores_map.get(c, '#bb86fc'))

    # Cria tabela de resumo - removidas colunas "Valor em R$" e "% do Total"
    resumo_data = [['Classificação', 'Quantidade']]

    for c, info in sorted(resumo.items()):
        resumo_data.append([
            info['nome'],
            str(info['count'])
        ])

    # Adiciona linha de total
    resumo_data.append([
        'TOTAL',
        str(total_itens)
    ])

    # Ajustadas larguras para formato vertical
    resumo_table = Table(resumo_data, colWidths=[10*cm, 6*cm])

    # Estilo básico da tabela
    resumo_style = create_table_style(has_header=True, alternating_colors=True)

    # Adiciona estilo para a linha de total
    resumo_style.add('BACKGROUND', (0, -1), (-1, -1), colors.HexColor('#f0f0f0'))
    resumo_style.add('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold')

    resumo_table.setStyle(resumo_style)
    elements.append(resumo_table)

    # Adiciona gráficos se houver dados
    if counts:
        elements.append(Spacer(1, 1*cm))
        elements.append(Paragraph("Distribuição por Classificação", styles['subtitle']))
        elements.append(Spacer(1, 0.3*cm))

        # Cria gráfico de pizza para distribuição por classificação
        # Removido o gráfico de distribuição por valor
        plt.figure(figsize=(7, 7))  # Dimensões quadradas para evitar deformação
        plt.pie(counts, labels=labels, autopct='%1.1f%%', startangle=90, colors=cores)
        plt.axis('equal')  # Mantém a proporção circular
        plt.title('Distribuição por Quantidade')

        # Adiciona um pouco mais de espaço ao redor do gráfico
        plt.tight_layout(pad=2.0)

        # Salva o gráfico em um buffer
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)

        # Adiciona o gráfico ao PDF com dimensões quadradas
        img = RLImage(img_buffer, width=12*cm, height=12*cm)
        elements.append(img)

        # Fecha a figura para liberar memória
        plt.close()

    elements.append(Spacer(1, 0.8*cm))

    # Lista detalhada de itens
    elements.append(Paragraph("Lista Detalhada de Itens", styles['subtitle']))
    elements.append(Spacer(1, 0.3*cm))

    # Dados da tabela - removida coluna "Valor em R$"
    data = [['Código', 'Cliente', 'Classificação', 'Dias Sem Mov.', 'Última Mov.', 'Estoque']]

    for analise in resultado:
        ultima_mov = analise.ultima_movimentacao.strftime('%d/%m/%Y') if analise.ultima_movimentacao else "Nunca"

        data.append([
            analise.mola.codigo,
            analise.mola.cliente,
            analise.get_classificacao_display(),
            str(analise.dias_sem_movimentacao),
            ultima_mov,
            str(analise.mola.quantidade_estoque)
        ])

    # Cria a tabela com larguras ajustadas para formato vertical
    table = Table(data, colWidths=[3*cm, 5*cm, 3.5*cm, 2.5*cm, 2.5*cm, 2*cm])

    # Aplica estilo avançado à tabela
    table.setStyle(create_table_style(has_header=True, alternating_colors=True))

    # Adiciona formatação condicional para classificação
    for i in range(1, len(data)):
        classificacao = data[i][2]
        if classificacao == "Obsoleto":
            table_style = TableStyle([
                ('TEXTCOLOR', (2, i), (2, i), colors.red),
                ('FONTNAME', (2, i), (2, i), 'Helvetica-Bold')
            ])
            table.setStyle(table_style)
        elif classificacao == "Crítico":
            table_style = TableStyle([
                ('TEXTCOLOR', (2, i), (2, i), colors.orange),
                ('FONTNAME', (2, i), (2, i), 'Helvetica-Bold')
            ])
            table.setStyle(table_style)

    elements.append(table)

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        titulo = "Análise de Obsolescência"
        add_header_footer(canvas, doc, titulo, filtro_texto)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response


def analise_obsolescencia_csv(request):
    """Gera CSV da análise de obsolescência"""
    # Obtém os filtros
    classificacao = request.GET.get('classificacao')

    # Filtra as análises
    analises = AnaliseObsolescencia.objects.all()

    if classificacao:
        analises = analises.filter(classificacao=classificacao)

    # Agrupa por mola (pega apenas a análise mais recente de cada mola)
    molas_ids = analises.values_list('mola_id', flat=True).distinct()
    resultado = []

    for mola_id in molas_ids:
        analise = analises.filter(mola_id=mola_id).order_by('-data_analise').first()
        if analise:
            resultado.append(analise)

    # Cria a resposta HTTP com o tipo de conteúdo CSV
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="analise_obsolescencia.csv"'

    # Cria o escritor CSV
    writer = csv.writer(response)

    # Cabeçalho - removida coluna "Valor (R$)"
    writer.writerow(['Código', 'Cliente', 'Classificação', 'Dias Sem Mov.', 'Última Mov.', 'Estoque'])

    # Dados
    for analise in resultado:
        ultima_mov = analise.ultima_movimentacao.strftime('%d/%m/%Y') if analise.ultima_movimentacao else "Nunca"

        writer.writerow([
            analise.mola.codigo,
            analise.mola.cliente,
            analise.get_classificacao_display(),
            analise.dias_sem_movimentacao,
            ultima_mov,
            analise.mola.quantidade_estoque
        ])

    return response
