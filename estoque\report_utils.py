"""
Utilitários para geração de relatórios PDF avançados.
"""
import os
import io
import datetime
import numpy as np
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
import matplotlib.pyplot as plt
from django.conf import settings

# Cores da Molas Rios
MOLAS_RIOS_COLORS = {
    'primary': '#bb86fc',  # Roxo/lilás
    'secondary': '#03dac6',  # Turquesa
    'background': '#121212',  # Fundo escuro
    'surface': '#1e1e1e',  # Superfície
    'error': '#cf6679',  # Erro
    'text_primary': 'rgba(255, 255, 255, 0.87)',  # Texto primário
    'text_secondary': 'rgba(255, 255, 255, 0.6)',  # Texto secundário
}

# Caminhos para as diferentes versões do logo
LOGO_PATHS = [
    # Nova logo (versão melhorada com fundo transparente)
    os.path.join('static', 'img', 'reports', 'logo_molas_rios_transparent.png'),

    # Nova logo (versão melhorada)
    os.path.join('static', 'img', 'reports', 'logo_molas_rios_enhanced.png'),

    # Nova logo (original)
    os.path.join('static', 'img', 'reports', 'logo_molas_rios_nova.jpg'),

    # Logo SVG
    os.path.join('static', 'img', 'reports', 'logo_molas_rios_oficial.svg'),

    # Logo original
    os.path.join('static', 'img', 'reports', 'logo_molas_rios_original.jpg'),

    # Logo padrão
    os.path.join('static', 'img', 'reports', 'logo_molas_rios.svg')
]

def get_styles():
    """Retorna estilos personalizados para os relatórios."""
    styles = getSampleStyleSheet()

    # Estilo para título
    title_style = ParagraphStyle(
        'TituloPersonalizado',
        parent=styles['Heading1'],
        fontSize=18,
        textColor=colors.HexColor(MOLAS_RIOS_COLORS['primary']),
        spaceAfter=12,
        spaceBefore=6,
        alignment=1  # Centralizado
    )

    # Estilo para subtítulo
    subtitle_style = ParagraphStyle(
        'SubtituloPersonalizado',
        parent=styles['Heading2'],
        fontSize=14,
        textColor=colors.HexColor(MOLAS_RIOS_COLORS['primary']),
        spaceAfter=10,
        spaceBefore=10
    )

    # Estilo para texto normal
    normal_style = ParagraphStyle(
        'TextoPersonalizado',
        parent=styles['Normal'],
        fontSize=11,
        leading=14,
        spaceAfter=8
    )

    # Estilo para informações adicionais
    info_style = ParagraphStyle(
        'InfoPersonalizado',
        parent=styles['Normal'],
        fontSize=9,
        leading=12,
        textColor=colors.HexColor('#888888')
    )

    # Estilo para rodapé
    footer_style = ParagraphStyle(
        'RodapePersonalizado',
        parent=styles['Normal'],
        fontSize=8,
        leading=10,
        textColor=colors.HexColor('#666666')
    )

    # Estilo para destaque
    highlight_style = ParagraphStyle(
        'DestaquePersonalizado',
        parent=styles['Normal'],
        fontSize=11,
        leading=14,
        textColor=colors.HexColor(MOLAS_RIOS_COLORS['secondary']),
        fontName='Helvetica-Bold'
    )

    return {
        'title': title_style,
        'subtitle': subtitle_style,
        'normal': normal_style,
        'info': info_style,
        'footer': footer_style,
        'highlight': highlight_style
    }

def add_header_footer(canvas, doc, titulo, filtros=None):
    """Adiciona cabeçalho e rodapé às páginas do relatório."""
    canvas.saveState()

    # Obtém o tamanho da página atual
    page_width, page_height = canvas._pagesize

    # Imprime informações de debug
    print(f"Adicionando cabeçalho e rodapé para: {titulo}")
    print(f"Tamanho da página: {page_width/cm}cm x {page_height/cm}cm")
    print(f"Orientação: {'Paisagem' if page_width > page_height else 'Retrato'}")

    # Cabeçalho
    # Logo da empresa - reduzida e reposicionada
    logo_loaded = False

    # Dimensões e posição ajustadas da logo
    logo_width = 3.5*cm  # Reduzida de 5cm para 3.5cm
    logo_height = 1.5*cm  # Reduzida de 2cm para 1.5cm
    logo_x = 1.5*cm
    logo_y = page_height - 2.2*cm  # Movida um pouco para cima

    # Tenta carregar cada logo na lista de caminhos
    for logo_path in LOGO_PATHS:
        try:
            canvas.drawImage(logo_path, logo_x, logo_y, width=logo_width, height=logo_height, preserveAspectRatio=True)
            logo_loaded = True
            print(f"Logo carregada com sucesso: {logo_path}")
            break  # Se conseguiu carregar, sai do loop
        except Exception as e:
            # Continua tentando o próximo logo
            print(f"Erro ao carregar logo {logo_path}: {str(e)}")
            continue

    # Se nenhum logo foi carregado, desenha um placeholder
    if not logo_loaded:
        print("Nenhuma logo carregada, usando placeholder")
        canvas.setFillColor(colors.HexColor(MOLAS_RIOS_COLORS['primary']))
        canvas.rect(logo_x, logo_y, logo_width, logo_height, fill=1)
        canvas.setFillColor(colors.white)
        canvas.setFont('Helvetica-Bold', 10)
        canvas.drawString(logo_x + 0.5*cm, logo_y + 0.7*cm, "MOLAS RIOS")

    # Título do relatório - movido para a direita e para baixo
    canvas.setFont('Helvetica-Bold', 16)
    canvas.setFillColor(colors.HexColor(MOLAS_RIOS_COLORS['primary']))
    canvas.drawString(6*cm, page_height - 1.3*cm, titulo)

    # Filtros aplicados (se houver) - também movidos para baixo
    if filtros:
        canvas.setFont('Helvetica', 9)
        canvas.setFillColor(colors.HexColor('#888888'))
        canvas.drawString(6*cm, page_height - 1.8*cm, filtros)

    # Linha separadora - movida para baixo para não interferir com o logo
    canvas.setStrokeColor(colors.HexColor(MOLAS_RIOS_COLORS['primary']))
    canvas.setLineWidth(1)
    canvas.line(1.5*cm, page_height - 2.9*cm, page_width - 1.5*cm, page_height - 2.9*cm)

    # Rodapé
    # Data e hora
    canvas.setFont('Helvetica', 8)
    canvas.setFillColor(colors.HexColor('#666666'))
    data_atual = datetime.datetime.now().strftime("%d/%m/%Y %H:%M")
    canvas.drawString(1.5*cm, 1.5*cm, f"Gerado em: {data_atual}")

    # Número da página
    # Nota: doc.page contém o número da página atual, mas não temos acesso ao total de páginas
    # durante a renderização, então mostramos apenas a página atual
    canvas.drawRightString(page_width - 1.5*cm, 1.5*cm, f"Página {doc.page}")

    # Linha separadora
    canvas.setStrokeColor(colors.HexColor(MOLAS_RIOS_COLORS['primary']))
    canvas.line(1.5*cm, 2*cm, page_width - 1.5*cm, 2*cm)

    canvas.restoreState()

def create_table_style(has_header=True, alternating_colors=True):
    """Cria um estilo de tabela personalizado."""
    style = [
        # Estilo básico
        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('GRID', (0, 0), (-1, -1), 0.5, colors.HexColor('#CCCCCC')),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
        ('TOPPADDING', (0, 0), (-1, -1), 3),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
        ('WORDWRAP', (0, 0), (-1, -1), True),
    ]

    # Adiciona estilo para o cabeçalho
    if has_header:
        style.extend([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(MOLAS_RIOS_COLORS['primary'])),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
            ('TOPPADDING', (0, 0), (-1, 0), 6),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ])

    # Adiciona cores alternadas para as linhas
    if alternating_colors:
        style.append(('ROWBACKGROUNDS', (0, 1 if has_header else 0), (-1, -1),
                     [colors.HexColor('#F8F8F8'), colors.white]))

    return TableStyle(style)

def create_chart(data, labels, title, xlabel='', ylabel='', figsize=(8, 4), color=None):
    """Cria um gráfico de barras personalizado."""
    if color is None:
        color = MOLAS_RIOS_COLORS['primary']

    # Configurar o estilo do matplotlib
    plt.style.use('seaborn-v0_8-darkgrid')

    # Criar figura com tamanho adequado
    fig, ax = plt.subplots(figsize=figsize)

    # Criar gráfico de barras
    barras = ax.bar(labels, data, color=color)

    # Adicionar valor em cima de cada barra
    for barra in barras:
        altura = barra.get_height()
        ax.text(barra.get_x() + barra.get_width()/2., altura + 0.1,
                f'{int(altura)}', ha='center', va='bottom', fontweight='bold')

    # Personalizar o gráfico
    ax.set_title(title, fontsize=14, fontweight='bold', color=MOLAS_RIOS_COLORS['primary'])
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.tick_params(axis='x', rotation=45)
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    # Ajustar layout
    plt.tight_layout()

    # Salvar em um buffer
    img_buffer = io.BytesIO()
    plt.savefig(img_buffer, format='png', dpi=300)
    img_buffer.seek(0)

    # Fechar a figura para liberar memória
    plt.close(fig)

    return Image(img_buffer, width=15*cm, height=8*cm)

def create_line_chart(data, labels, title, xlabel='', ylabel='', figsize=(10, 5), color=None, marker='o'):
    """Cria um gráfico de linha personalizado para mostrar tendências."""
    if color is None:
        color = MOLAS_RIOS_COLORS['secondary']

    # Configurar o estilo do matplotlib
    plt.style.use('seaborn-v0_8-darkgrid')

    # Criar figura com tamanho adequado
    fig, ax = plt.subplots(figsize=figsize)

    # Criar gráfico de linha
    line = ax.plot(labels, data, marker=marker, linestyle='-', linewidth=2, color=color)

    # Adicionar pontos de dados
    for i, valor in enumerate(data):
        ax.text(i, valor + max(data) * 0.02, f'{int(valor)}',
                ha='center', va='bottom', fontweight='bold', fontsize=9)

    # Personalizar o gráfico
    ax.set_title(title, fontsize=14, fontweight='bold', color=MOLAS_RIOS_COLORS['primary'])
    ax.set_xlabel(xlabel, fontsize=12)
    ax.set_ylabel(ylabel, fontsize=12)
    ax.tick_params(axis='x', rotation=45)
    ax.grid(True, linestyle='--', alpha=0.7)

    # Adicionar linha de tendência
    if len(data) > 1:
        # Calcular linha de tendência usando regressão linear
        x = list(range(len(data)))
        z = np.polyfit(x, data, 1)
        p = np.poly1d(z)
        trend_line = p(x)

        # Plotar linha de tendência
        ax.plot(labels, trend_line, linestyle='--', color='red', alpha=0.7,
                label=f'Tendência ({z[0]:.1f}x{z[1]:+.1f})')

        # Adicionar legenda
        ax.legend(loc='best')

    # Ajustar layout
    plt.tight_layout()

    # Salvar em um buffer
    img_buffer = io.BytesIO()
    plt.savefig(img_buffer, format='png', dpi=300)
    img_buffer.seek(0)

    # Fechar a figura para liberar memória
    plt.close(fig)

    return Image(img_buffer, width=16*cm, height=9*cm)


def create_pdf_document(response, pagesize=A4, landscape_mode=False, title="Relatório", filters=None, elements=None):
    """
    Função auxiliar para criar documentos PDF padronizados com cabeçalho e rodapé.

    Args:
        response: Objeto HttpResponse para o PDF
        pagesize: Tamanho da página (padrão: A4)
        landscape_mode: Se True, usa orientação paisagem
        title: Título do relatório
        filters: Filtros aplicados (opcional)
        elements: Lista de elementos a serem adicionados ao PDF (opcional)

    Returns:
        Objeto HttpResponse com o PDF gerado
    """
    # Aplica orientação paisagem se solicitado
    if landscape_mode:
        pagesize = landscape(pagesize)

    # Cria o documento PDF com margens adequadas para cabeçalho e rodapé
    doc = SimpleDocTemplate(response, pagesize=pagesize,
                           leftMargin=1.5*cm, rightMargin=1.5*cm,
                           topMargin=2.5*cm, bottomMargin=2.5*cm)

    # Inicializa a lista de elementos se não fornecida
    if elements is None:
        elements = []

    # Função para adicionar cabeçalho e rodapé
    def add_header_footer_to_pdf(canvas, doc):
        add_header_footer(canvas, doc, title, filters)

    # Constrói o PDF com cabeçalho e rodapé
    doc.build(elements, onFirstPage=add_header_footer_to_pdf, onLaterPages=add_header_footer_to_pdf)

    return response
