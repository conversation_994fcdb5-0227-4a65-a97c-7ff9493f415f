"""
Utilitários para o aplicativo de estoque
"""
import re
from decimal import Decimal


def extrair_valor_numerico_diametro(diametro_str):
    """
    Extrai o valor numérico de uma string de diâmetro.
    
    Exemplos:
    - "0,10 mm" -> 0.1
    - "0.20 mm" -> 0.2
    - "Ø 0,30 mm" -> 0.3
    - "1,00 mm" -> 1.0
    - "1.50 mm" -> 1.5
    
    Args:
        diametro_str (str): String contendo o diâmetro
        
    Returns:
        float: Valor numérico do diâmetro ou 0 se não for possível extrair
    """
    if not diametro_str:
        return 0
    
    # Substituir vírgulas por pontos para garantir formato decimal correto
    diametro_str = diametro_str.replace(',', '.')
    
    # Remover caracteres não numéricos e manter apenas números e pontos
    valor_numerico = re.sub(r'[^\d.]', '', diametro_str)
    
    try:
        return float(valor_numerico)
    except (ValueError, TypeError):
        return 0


def ordenar_materiais(materiais):
    """
    Ordena uma lista de materiais por nome e diâmetro numérico.
    
    Args:
        materiais (list): Lista de objetos Material ou dicionários com chaves 'nome' e 'diametro'
        
    Returns:
        list: Lista ordenada de materiais
    """
    # Verificar se estamos lidando com objetos Material ou dicionários
    if materiais and hasattr(materiais[0], 'nome'):
        # Objetos Material
        return sorted(materiais, key=lambda m: (m.nome, extrair_valor_numerico_diametro(m.diametro)))
    else:
        # Dicionários
        return sorted(materiais, key=lambda m: (m.get('nome', ''), extrair_valor_numerico_diametro(m.get('diametro', ''))))


def formatar_decimal(valor, casas_decimais=2):
    """
    Formata um valor decimal para exibição.
    
    Args:
        valor: Valor a ser formatado (pode ser Decimal, float, int ou string)
        casas_decimais (int): Número de casas decimais
        
    Returns:
        str: Valor formatado
    """
    try:
        # Converter para Decimal se não for
        if not isinstance(valor, Decimal):
            valor = Decimal(str(valor))
        
        # Formatar com o número correto de casas decimais
        formato = f'{{:.{casas_decimais}f}}'
        return formato.format(valor)
    except:
        return str(valor)
